package com.gl.service.shop.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.web.response.Result;
import com.gl.service.shop.controller.request.FollowAnchorReq;
import com.gl.service.shop.controller.request.FollowBgmReq;
import com.gl.service.shop.controller.request.TextTemplateAddReq;
import com.gl.service.shop.controller.request.TextTemplateDelReq;
import com.gl.service.shop.service.WeChatService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * WeChatController单元测试类
 * 测试微信控制器的所有REST端点，包括请求处理、响应验证、异常处理等功能
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("微信控制器单元测试")
class WeChatControllerTest {

    @Mock
    private WeChatService weChatService;

    @InjectMocks
    private WeChatController weChatController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    private static final String BASE_URL = "/api/wechat";

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(weChatController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    @DisplayName("测试微信登录接口 - 成功场景")
    void testWebLogon_Success() throws Exception {
        // Given
        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("loginUrl",
                "https://open.weixin.qq.com/connect/qrconnect?appid=test&redirect_uri=test");
        expectedResponse.put("state", "test-state");

        when(weChatService.webLogin(any(HttpServletRequest.class))).thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(get(BASE_URL + "/login"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.loginUrl").exists())
                .andExpect(jsonPath("$.state").exists());

        verify(weChatService).webLogin(any(HttpServletRequest.class));
    }

    @Test
    @DisplayName("测试微信回调接口 - 成功场景")
    void testCallback_Success() throws Exception {
        // Given
        String code = "test_code";
        String state = "test_state";
        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("token", "jwt_token");

        when(weChatService.handleCallback(code, state)).thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(get(BASE_URL + "/callback")
                        .param("code", code)
                        .param("state", state))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.token").value("jwt_token"));

        verify(weChatService).handleCallback(code, state);
    }

    @Test
    @DisplayName("测试微信回调接口 - 缺少参数")
    void testCallback_MissingParameters() throws Exception {
        // When & Then
        mockMvc.perform(get(BASE_URL + "/callback"))
                .andExpect(status().isBadRequest());

        verify(weChatService, never()).handleCallback(anyString(), anyString());
    }

    @Test
    @DisplayName("测试检查登录状态接口 - 成功场景")
    void testCheckLoginStatus_Success() throws Exception {
        // Given
        String uuid = "test-uuid";
        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("loggedIn", true);
        expectedResponse.put("token", "jwt_token");

        when(weChatService.checkLoginStatus(uuid)).thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(get(BASE_URL + "/check-login/{uuid}", uuid))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.loggedIn").value(true))
                .andExpect(jsonPath("$.token").value("jwt_token"));

        verify(weChatService).checkLoginStatus(uuid);
    }

    @Test
    @DisplayName("测试更新微信信息接口 - 成功场景")
    void testUpdateWx_Success() throws Exception {
        // Given
        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("loginUrl",
                "https://open.weixin.qq.com/connect/qrconnect?appid=test&redirect_uri=test/user/profile");
        expectedResponse.put("state", "test-state");

        when(weChatService.updateWx(any(HttpServletRequest.class))).thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(get(BASE_URL + "/updateWx"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.loginUrl").exists())
                .andExpect(jsonPath("$.state").exists());

        verify(weChatService).updateWx(any(HttpServletRequest.class));
    }

    @Test
    @DisplayName("测试更新回调接口 - 成功场景")
    void testUpdateCallback_Success() throws Exception {
        // Given
        String code = "test_code";
        String state = "test_state";
        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("success", true);

        when(weChatService.updateCallback(code)).thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(get(BASE_URL + "/update/callback")
                        .param("code", code)
                        .param("state", state))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(weChatService).updateCallback(code);
    }

    @Test
    @DisplayName("测试获取我的长音频主播接口 - 成功场景")
    void testGetMyLongAnchor_Success() throws Exception {
        // Given
        Result expectedResult = Result.success("test_data");
        when(weChatService.getMyLongAnchor()).thenReturn(expectedResult);

        // When & Then
        mockMvc.perform(get(BASE_URL + "/getMyLongAnchor"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.data").value("test_data"));

        verify(weChatService).getMyLongAnchor();
    }

    @Test
    @DisplayName("测试获取我的VIP主播接口 - 成功场景")
    void testGetMyVipAnchor_Success() throws Exception {
        // Given
        Result expectedResult = Result.success("test_data");
        when(weChatService.getMyVipAnchor()).thenReturn(expectedResult);

        // When & Then
        mockMvc.perform(get(BASE_URL + "/getMyAnchor"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.data").value("test_data"));

        verify(weChatService).getMyVipAnchor();
    }

    @Test
    @DisplayName("测试关注主播接口 - 成功场景")
    void testFollowAnchor_Success() throws Exception {
        // Given
        FollowAnchorReq request = new FollowAnchorReq();
        request.setAnchorId(1L);

        Result expectedResult = Result.success(null);
        when(weChatService.followAnchor(any(FollowAnchorReq.class))).thenReturn(expectedResult);

        // When & Then
        mockMvc.perform(post(BASE_URL + "/followAnchor")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(weChatService).followAnchor(argThat(req -> req.getAnchorId().equals(1L)));
    }

    @Test
    @DisplayName("测试关注主播接口 - 无效请求体")
    void testFollowAnchor_InvalidRequestBody() throws Exception {
        // When & Then
        mockMvc.perform(post(BASE_URL + "/followAnchor")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid_json"))
                .andExpect(status().isBadRequest());

        verify(weChatService, never()).followAnchor(any(FollowAnchorReq.class));
    }

    @Test
    @DisplayName("测试关注长音频主播接口 - 成功场景")
    void testFollowLongAnchor_Success() throws Exception {
        // Given
        FollowAnchorReq request = new FollowAnchorReq();
        request.setAnchorId(2L);

        Result expectedResult = Result.success(null);
        when(weChatService.followLongAnchor(any(FollowAnchorReq.class))).thenReturn(expectedResult);

        // When & Then
        mockMvc.perform(post(BASE_URL + "/followLong")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(weChatService).followLongAnchor(argThat(req -> req.getAnchorId().equals(2L)));
    }

    @Test
    @DisplayName("测试关注背景音乐接口 - 成功场景")
    void testFollowBgm_Success() throws Exception {
        // Given
        FollowBgmReq request = new FollowBgmReq();
        request.setBgmId(3L);

        Result expectedResult = Result.success(null);
        when(weChatService.followBgm(any(FollowBgmReq.class))).thenReturn(expectedResult);

        // When & Then
        mockMvc.perform(post(BASE_URL + "/followBgm")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(weChatService).followBgm(argThat(req -> req.getBgmId().equals(3L)));
    }

    @Test
    @DisplayName("测试获取关注的背景音乐接口 - 成功场景")
    void testGetFollowBgm_Success() throws Exception {
        // Given
        Result expectedResult = Result.success("bgm_data");
        when(weChatService.getFollowBgm()).thenReturn(expectedResult);

        // When & Then
        mockMvc.perform(get(BASE_URL + "/getFollowBgm"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.data").value("bgm_data"));

        verify(weChatService).getFollowBgm();
    }

    @Test
    @DisplayName("测试添加文本模板接口 - 成功场景")
    void testAddTextTemplate_Success() throws Exception {
        // Given
        TextTemplateAddReq request = new TextTemplateAddReq();
        request.setTextContent("测试模板内容");

        Result expectedResult = Result.success(true);
        when(weChatService.addTextTemplate(any(TextTemplateAddReq.class))).thenReturn(expectedResult);

        // When & Then
        mockMvc.perform(post(BASE_URL + "/addTextTemplate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.data").value(true));

        verify(weChatService).addTextTemplate(argThat(req -> req.getTextContent().equals("测试模板内容")));
    }

    @Test
    @DisplayName("测试获取文本模板接口 - 成功场景")
    void testGetTextTemplate_Success() throws Exception {
        // Given
        Result expectedResult = Result.success("template_data");
        when(weChatService.getTextTemplate()).thenReturn(expectedResult);

        // When & Then
        mockMvc.perform(get(BASE_URL + "/getTextTemplate"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.data").value("template_data"));

        verify(weChatService).getTextTemplate();
    }

    @Test
    @DisplayName("测试删除文本模板接口 - 成功场景")
    void testDelTextTemplate_Success() throws Exception {
        // Given
        TextTemplateDelReq request = new TextTemplateDelReq();
        request.setId(1L);

        Result expectedResult = Result.success(true);
        when(weChatService.delTextTemplate(any(TextTemplateDelReq.class))).thenReturn(expectedResult);

        // When & Then
        mockMvc.perform(delete(BASE_URL + "/delTextTemplate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.data").value(true));

        verify(weChatService).delTextTemplate(argThat(req -> req.getId().equals(1L)));
    }

    @Test
    @DisplayName("测试删除文本模板接口 - 无效请求体")
    void testDelTextTemplate_InvalidRequestBody() throws Exception {
        // When & Then
        mockMvc.perform(delete(BASE_URL + "/delTextTemplate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andExpect(status().isOk()); // 即使ID为null，控制器也会调用服务

        verify(weChatService).delTextTemplate(any(TextTemplateDelReq.class));
    }

    @Test
    @DisplayName("测试服务异常处理 - 微信回调异常")
    void testCallback_ServiceException() throws Exception {
        // Given
        String code = "test_code";
        String state = "test_state";

        when(weChatService.handleCallback(code, state))
                .thenThrow(new RuntimeException("微信API调用失败"));

        // When & Then - 期望抛出异常
        try {
            mockMvc.perform(get(BASE_URL + "/callback")
                    .param("code", code)
                    .param("state", state));
        } catch (Exception e) {
            // 验证异常被抛出
            assert e.getCause() instanceof RuntimeException;
            assert "微信API调用失败".equals(e.getCause().getMessage());
        }

        verify(weChatService).handleCallback(code, state);
    }

    @Test
    @DisplayName("测试服务异常处理 - 更新回调异常")
    void testUpdateCallback_ServiceException() throws Exception {
        // Given
        String code = "test_code";
        String state = "test_state";

        when(weChatService.updateCallback(code))
                .thenThrow(new RuntimeException("更新失败"));

        // When & Then - 期望抛出异常
        try {
            mockMvc.perform(get(BASE_URL + "/update/callback")
                    .param("code", code)
                    .param("state", state));
        } catch (Exception e) {
            // 验证异常被抛出
            assert e.getCause() instanceof RuntimeException;
            assert "更新失败".equals(e.getCause().getMessage());
        }

        verify(weChatService).updateCallback(code);
    }

    @Test
    @DisplayName("测试HTTP方法不支持 - POST请求GET端点")
    void testMethodNotAllowed_PostToGetEndpoint() throws Exception {
        // When & Then
        mockMvc.perform(post(BASE_URL + "/login"))
                .andExpect(status().isMethodNotAllowed());

        verify(weChatService, never()).webLogin(any(HttpServletRequest.class));
    }

    @Test
    @DisplayName("测试HTTP方法不支持 - GET请求POST端点")
    void testMethodNotAllowed_GetToPostEndpoint() throws Exception {
        // When & Then
        mockMvc.perform(get(BASE_URL + "/followAnchor"))
                .andExpect(status().isMethodNotAllowed());

        verify(weChatService, never()).followAnchor(any(FollowAnchorReq.class));
    }

    @Test
    @DisplayName("测试Content-Type不支持 - 错误的媒体类型")
    void testUnsupportedMediaType() throws Exception {
        // Given
        FollowAnchorReq request = new FollowAnchorReq();
        request.setAnchorId(1L);

        // When & Then
        mockMvc.perform(post(BASE_URL + "/followAnchor")
                        .contentType(MediaType.TEXT_PLAIN)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isUnsupportedMediaType());

        verify(weChatService, never()).followAnchor(any(FollowAnchorReq.class));
    }

    @Test
    @DisplayName("测试路径变量验证 - 空UUID")
    void testCheckLoginStatus_EmptyUuid() throws Exception {
        // Given - 空UUID会导致404，因为路径变量为空
        String emptyUuid = "";

        // When & Then - 期望404状态码，因为空路径变量无法匹配路径模式
        mockMvc.perform(get(BASE_URL + "/check-login/{uuid}", emptyUuid))
                .andExpect(status().isNotFound());

        // 验证服务方法没有被调用
        verify(weChatService, never()).checkLoginStatus(anyString());
    }

    @Test
    @DisplayName("测试请求参数验证 - 特殊字符")
    void testCallback_SpecialCharacters() throws Exception {
        // Given
        String code = "test@#$%code";
        String state = "test&*()state";
        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("token", "jwt_token");

        when(weChatService.handleCallback(code, state)).thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(get(BASE_URL + "/callback")
                        .param("code", code)
                        .param("state", state))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.token").value("jwt_token"));

        verify(weChatService).handleCallback(code, state);
    }

    @Test
    @DisplayName("测试大数据量请求 - 长文本模板")
    void testAddTextTemplate_LargeContent() throws Exception {
        // Given
        TextTemplateAddReq request = new TextTemplateAddReq();
        StringBuilder largeContent = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            largeContent.append("这是一个很长的模板内容测试");
        }
        request.setTextContent(largeContent.toString());

        Result expectedResult = Result.success(true);
        when(weChatService.addTextTemplate(any(TextTemplateAddReq.class))).thenReturn(expectedResult);

        // When & Then
        mockMvc.perform(post(BASE_URL + "/addTextTemplate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(weChatService).addTextTemplate(argThat(req -> req.getTextContent().length() > 10000));
    }

    @Test
    @DisplayName("测试并发请求处理 - 多个关注请求")
    void testConcurrentFollowRequests() throws Exception {
        // Given
        FollowAnchorReq request1 = new FollowAnchorReq();
        request1.setAnchorId(1L);

        FollowAnchorReq request2 = new FollowAnchorReq();
        request2.setAnchorId(2L);

        Result expectedResult = Result.success(null);
        when(weChatService.followAnchor(any(FollowAnchorReq.class))).thenReturn(expectedResult);

        // When & Then - 模拟并发请求
        mockMvc.perform(post(BASE_URL + "/followAnchor")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isOk());

        mockMvc.perform(post(BASE_URL + "/followAnchor")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isOk());

        verify(weChatService, times(2)).followAnchor(any(FollowAnchorReq.class));
    }
}
