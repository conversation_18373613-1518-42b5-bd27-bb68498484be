package com.gl.service.commercial.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gl.framework.common.util.sql.SqlUtils;
import com.gl.framework.web.response.Result;
import com.gl.service.commercial.vo.CommercialVo;
import com.gl.service.commercial.vo.ExcelCommercial;
import com.gl.service.commercial.vo.dto.CommercialDto;
import com.gl.util.CenterCellWriteHandler;
import com.gl.util.GetShopRefUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CommercialService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private GetShopRefUtil shopRefUtil;

    public Result list(CommercialDto dto, Integer exportType) {
        Result result = Result.success();
        if (shopRefUtil.isNeedWxFilter()) {
            result.addData("total", 0);
            result.addData("result", new ArrayList<>());
            return result;
        }
        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();
        if (dto != null) {
            if (StringUtils.isNotBlank(dto.getShopName())) {
                where.append(" AND s.shop_name like ? ");
                args.add("%" + dto.getShopName() + "%");
            }
            if (dto.getShopId() != null) {
                where.append(" AND s.id = ? ");
                args.add(dto.getShopId());
            }
            if (StringUtils.isNotBlank(dto.getSearchCondition())) {
                where.append(" AND (wu.nickname like ? or wu.phone like ?) ");
                args.add("%" + dto.getSearchCondition() + "%");
                args.add("%" + dto.getSearchCondition() + "%");
            }

            if (com.gl.framework.common.util.StringUtils.isNotBlank(dto.getBeginTime())) {
                where.append(" AND wu.auth_time >= ? ");
                args.add(dto.getBeginTime());
            }
            if (com.gl.framework.common.util.StringUtils.isNotBlank(dto.getEndTime())) {
                where.append(" AND wu.auth_time <= ? ");
                args.add(dto.getEndTime());
            }
        }

        List<Long> shopRef = shopRefUtil.getShopRef();
        if (CollUtil.isNotEmpty(shopRef)) {
            where.append(" AND s.id in ").append(SqlUtils.foreachIn(shopRef.size()));
            args.addAll(shopRef);
        }

        log.info("args = {}", args);

        String sql = "select " + "wu.id," + "wu.avatar," + "wu.nickname," + "wu.phone," + "wu.gender," + "wu.auth_time,"
                + "group_concat(DISTINCT(s.shop_name)) shopNames," + "group_concat(DISTINCT(d.name)) deviceNames, "
                + "count(DISTINCT(d.id)) deviceNum " + "from dub_wechat_user wu "
                + "left join dub_user_shop_ref usr on wu.id= usr.user_id " + "left join dub_shop s on s.id=usr.shop_id "
                + "left join dub_device d on d.user_id=wu.id "
                + "WHERE (wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL)";

        where.append("GROUP BY wu.id ");
        Long count = jdbcTemplate.queryForObject("select count(1) from (" + sql + where + ") t", Long.class,
                args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result", new ArrayList<>());
            return result;
        }
        where.append("order by wu.auth_time DESC ");
        if (exportType == 1) {
            where.append(" LIMIT ? OFFSET ? ");
            assert dto != null;
            args.add(dto.getPageSize());
            args.add(dto.getPageNumber() * dto.getPageSize());
        }
        log.info("查询，sql:{}", sql + where);
        log.info("查询，args:{}", args);
        List<CommercialVo> deviceVos = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(CommercialVo.class),
                args.toArray());
        result.addData("total", count);
        result.addData("result", deviceVos);
        return result;
    }

    public void exportList(CommercialDto dto, HttpServletResponse response) throws IOException {
        Result list = list(dto, 2);
        HashMap hashMap = JSONObject.parseObject(JSONObject.toJSONString(list.getData()), HashMap.class);
        Object result = hashMap.get("result");
        List<ExcelCommercial> excelCommercials = null;
        if (result != null) {
            List<CommercialVo> deviceVos = JSON.parseArray(JSON.toJSONString(result), CommercialVo.class);
            excelCommercials = deviceVos.stream().map(item -> {
                ExcelCommercial excelCommercial = new ExcelCommercial();
                // excelCommercial.setAvatar(item.getAvatar());
                excelCommercial.setNickname(StringUtils.isBlank(item.getNickname()) ? "" : item.getNickname());
                excelCommercial.setPhone(StringUtils.isBlank(item.getPhone()) ? "" : item.getPhone());
                excelCommercial.setGender(
                        item.getGender() == 0 ? "未知" : item.getGender() == 1 ? "男" : item.getGender() == 2 ? "女" : "");
                excelCommercial.setAuthTime(item.getAuthTime() == null ? ""
                        : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(item.getAuthTime()));
                excelCommercial.setShopNames(item.getShopNames());
                excelCommercial.setDeviceNames(item.getDeviceNames());
                return excelCommercial;
            }).collect(Collectors.toList());
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = "商户管理_" + System.currentTimeMillis();
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ExcelTypeEnum.XLSX.getValue());
        EasyExcel.write(response.getOutputStream(), ExcelCommercial.class)
                .registerWriteHandler(new CenterCellWriteHandler()).sheet("商户管理").doWrite(excelCommercials);
        // 导出excel
        log.info("设备管理导出end");
    }

}
