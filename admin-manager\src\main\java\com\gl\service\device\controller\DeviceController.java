package com.gl.service.device.controller;

import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.web.response.Result;
import com.gl.service.device.service.DeviceService;
import com.gl.service.device.service.RemoteDeviceService;
import com.gl.service.device.vo.DelAudioParams;
import com.gl.service.device.vo.DeviceVo;
import com.gl.service.device.vo.UpdateVolumeParams;
import com.gl.service.device.vo.dto.DeviceAddWorkDto;
import com.gl.service.device.vo.dto.DeviceDto;
import com.gl.service.device.vo.dto.DeviceUpdateVolume;
import com.gl.service.device.vo.dto.DeviceVoiceDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 设备管理
 *
 * @author: duanjinze
 * @date: 2022/11/11 9:54
 * @version: 1.0
 */
@Controller
@RequestMapping("/device")
public class DeviceController {

    @Autowired
    private DeviceService deviceService;

    @Resource
    private RemoteDeviceService remoteDeviceService;

    /**
     * 设备列表
     * exportType 1不导出 2导出
     *
     * @param dto
     * @return
     */
    @GetMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('device:device:list')")
    public Result list(DeviceDto dto) {
        return deviceService.list(dto, 1);
    }

    /**
     * 导出设备列表
     *
     * @param dto
     * @param response
     * @throws IOException
     */
    @PostMapping("/export")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('device:device:export')")
    public void exportList(@RequestBody DeviceDto dto, HttpServletResponse response) throws IOException {
        deviceService.exportList(dto, response);
    }

    /**
     * 新增或者修改设备
     *
     * @param vo
     * @return
     */
    @PostMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('device:device:add')")
    public Result addOrUpdate(@RequestBody DeviceVo vo) {
        if (vo.getId() == null) {
            return deviceService.add(vo);
        } else {
            return deviceService.update(vo);
        }
    }

    /**
     * 解绑与绑定设备
     *
     * @param vo
     * @return
     */
    @PutMapping("/bind")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('device:device:bind')")
    public Result bindAndUntie(@RequestBody DeviceVo vo) {
        return deviceService.bindAndUntie(vo);
    }

    /**
     * 修改使用状态
     *
     * @param dto
     * @return
     */
    @PutMapping("/use")
    @ResponseBody
    public Result updateUseStatus(@RequestBody DeviceDto dto) {
        return deviceService.updateUseStatus(dto);
    }

    /**
     * 删除设备
     *
     * @param dto
     * @return
     */
    @DeleteMapping
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('device:device:delete')")
    public Result delete(@RequestBody DeviceDto dto) {
        return deviceService.delete(dto);
    }

    /**
     * 设备详情
     *
     * @param deviceId 设备表id
     * @return
     */
    @GetMapping("/detail")
    @ResponseBody
    public Result detail(@RequestParam(name = "deviceId") Long deviceId) {
        return deviceService.detail(deviceId);
    }

    /**
     * 删除设备与语音包表
     *
     * @param dto id 设备与语音包表id
     * @return
     */
    @DeleteMapping("/deviceAndVoice")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('device:delete:deviceandvoice')")
    public Result deleteDeviceAndVoice(@RequestBody DeviceVoiceDto dto) {
        return deviceService.deleteDeviceAndVoice(dto);
    }

    /**
     * 修改排序
     *
     * @param dto
     * @return
     */
    @PutMapping("/sortby")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('device:update:sortby')")
    public Result updateSortBy(@RequestBody DeviceVoiceDto dto) {
        return deviceService.updateSortBy(dto);
    }

    /**
     * 下拉框获取设备
     *
     * @return
     */
    @GetMapping("/getDeviceList")
    @ResponseBody
    public Result getDeviceList() {
        return deviceService.getDeviceList();
    }

    /***
     * 解绑门店
     *
     * @param vo
     * @return
     */
    @PutMapping("/relieveShop")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('device:device:relieveShop')")
    public Result relieveShop(@RequestBody DeviceVo vo) {
        return deviceService.relieveShop(vo);
    }

    /***
     * 设备添加作品
     *
     * @param dto
     * @return
     */
    @PostMapping("/addWork")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('device:device:addWork')")
    public Result addWork(@RequestBody DeviceAddWorkDto dto) {
        return deviceService.addWork(dto);
    }

    /**
     * 删除设备作品
     *
     * @param dto
     * @return
     */
    @PostMapping("/delWork")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('device:device:delWork')")
    public Result delWork(@RequestBody DeviceAddWorkDto dto) {
        return deviceService.delWork(dto);
    }

    /**
     * 批量修改音量
     *
     * @param dto
     * @return
     */
    @PostMapping("/updateVolume")
    @ResponseBody
    @PreAuthorize("@ps.hasPermi('device:device:updateVolume')")
    public Result updateVolume(@RequestBody DeviceUpdateVolume dto) {
        return deviceService.updateVolume(dto);
    }

    /**
     * 用户可选设备
     *
     * @return
     */
    @GetMapping("/my")
    @ResponseBody
    public Result getUserDeviceSelect() {
        return deviceService.getUserDeviceSelect();
    }

    /**
     * 用户可选设备
     *
     * @return
     */
    @GetMapping("/tree/my")
    @ResponseBody
    public Result getTreeUserDeviceSelect() {
        return deviceService.getTreeUserDeviceSelect();
    }

    /**
     * 更新音量
     *
     * @return
     */
    @PostMapping("/updateSingleVolume")
    @ResponseBody
    public Result updateVolume(@RequestBody UpdateVolumeParams params) {
        return remoteDeviceService.updateVolume(params, SecurityUtils.getLoginUser().getUser().getSiteId());
    }

    /**
     * 删除音频
     *
     * @return
     */
    @PostMapping("/delAudio")
    @ResponseBody
    public Result delAudio(@RequestBody DelAudioParams params) {
        return remoteDeviceService.delAudio(params, SecurityUtils.getLoginUser().getUser().getSiteId());
    }

    /**
     * 获取时间
     * -- 应该是硬件开机获取时间吧
     *
     * @return
     */
    @GetMapping("/getTime")
    @ResponseBody
    public Result getTime(@RequestParam("deviceId") Long deviceId) {
        return remoteDeviceService.getTime(deviceId, SecurityUtils.getLoginUser().getUser().getSiteId());
    }

    /**
     * 获取音乐列表
     *
     * @return
     */
    @GetMapping("/getMusic")
    @ResponseBody
    public Result getMusic(@RequestParam("deviceId") Long deviceId) {
        return remoteDeviceService.getMusic(deviceId, SecurityUtils.getLoginUser().getUser().getSiteId());
    }

    /**
     * 试听音频
     *
     * @return
     */
    @GetMapping("/listenAudio")
    @ResponseBody
    public Result listenAudio(@RequestParam("deviceId") Long deviceId, @RequestParam("audioName") String audioName) {
        return remoteDeviceService.listenAudio(deviceId, audioName, SecurityUtils.getLoginUser().getUser().getSiteId());
    }

    /**
     * 获取剩余空间
     *
     * @return
     */
    @GetMapping("/getSpace")
    @ResponseBody
    public Result getSpace(@RequestParam("deviceId") Long deviceId) {
        return remoteDeviceService.getSpace(deviceId, SecurityUtils.getLoginUser().getUser().getSiteId());
    }

    /**
     * 获取音量
     *
     * @return
     */
    @GetMapping("/getVolume")
    @ResponseBody
    public Result getVolume(@RequestParam("deviceId") Long deviceId) {
        return remoteDeviceService.getVolume(deviceId, SecurityUtils.getLoginUser().getUser().getSiteId());
    }

    /**
     * 定时播放
     *
     * @return
     */
    @GetMapping("/setTimePlay")
    @ResponseBody
    public Result setTimePlay(@RequestParam("deviceId") Long deviceId, @RequestParam("audioName") String audioName) {
        return remoteDeviceService.setTimePlay(deviceId, audioName);
    }

    /**
     * 复制音频
     *
     * @return
     */
    @GetMapping("/transmissionAudio")
    @ResponseBody
    public Result transmissionAudio(@RequestParam("deviceId") Long deviceId) {
        return remoteDeviceService.transmissionAudio(deviceId);
    }

    /**
     * 发送音频
     *
     * @return
     */
    @GetMapping("/sendAudio")
    @ResponseBody
    public Result sendAudio(@RequestParam("deviceId") Long deviceId, @RequestParam("audioUrl") String audioUrl,
            @RequestParam("name") String name) {
        return remoteDeviceService.sendAudio(deviceId, audioUrl, SecurityUtils.getLoginUser().getUser().getSiteId(),
                name);
    }

    /**
     * 扫描蓝牙
     *
     * @return
     */
    @GetMapping("/scanBluetooth")
    @ResponseBody
    public Result scanBluetooth(@RequestParam("deviceId") Long deviceId) {
        return remoteDeviceService.scanBluetooth(deviceId, SecurityUtils.getLoginUser().getUser().getSiteId());
    }

    /**
     * 获取蓝牙 列表
     *
     * @return
     */
    @GetMapping("/bluetoothList")
    @ResponseBody
    public Result getBluetooth(@RequestParam("deviceId") Long deviceId) {
        return remoteDeviceService.getBluetooth(deviceId, SecurityUtils.getLoginUser().getUser().getSiteId());
    }
}
