# Created at 2025-08-05T10:40:05.383
TestSet has not finished before stream error has appeared >> initializing exit by non-null configuration: null
java.io.EOFException
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at org.apache.maven.surefire.booter.MasterProcessCommand.decode(MasterProcessCommand.java:115)
	at org.apache.maven.surefire.booter.CommandReader$CommandRunnable.run(CommandReader.java:391)
	at java.lang.Thread.run(Thread.java:750)


# Created at 2025-08-05T10:40:10.039
Unexpected IOException with stream: 10:40:10.038 [31mWARN [0;39m [main] o.s.b.a.f.FreeMarkerAutoConfiguration - Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)


# Created at 2025-08-05T10:40:11.834
Unexpected IOException with stream: 10:40:11.816 [34mINFO [0;39m [main] o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6a35f9fd, org.springframework.security.web.context.SecurityContextPersistenceFilter@1a4e3cbb, org.springframework.security.web.header.HeaderWriterFilter@1e1e77ed, org.springframework.security.web.authentication.logout.LogoutFilter@6342aaab, com.gl.framework.security.filter.JwtAuthenticationTokenFilter@1e4d3c2e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@31caf651, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@49f4de0e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@578fb678, org.springframework.security.web.session.SessionManagementFilter@3c9b416a, org.springframework.security.web.access.ExceptionTranslationFilter@3cf6f3aa, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@114f913d]


# Created at 2025-08-05T10:40:12.182
Unexpected IOException with stream: 10:40:12.181 [34mINFO [0;39m [main] o.s.b.t.m.w.SpringBootMockServletContext - Initializing Spring TestDispatcherServlet ''


# Created at 2025-08-05T10:40:12.183
Unexpected IOException with stream: 10:40:12.182 [34mINFO [0;39m [main] o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''


# Created at 2025-08-05T10:40:12.188
Unexpected IOException with stream: 10:40:12.187 [34mINFO [0;39m [main] o.s.t.w.s.TestDispatcherServlet - Completed initialization in 4 ms


# Created at 2025-08-05T10:40:13.367
Unexpected IOException with stream: 10:40:13.362 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel


# Created at 2025-08-05T10:40:13.371
Unexpected IOException with stream: 10:40:13.368 [34mINFO [0;39m [main] o.s.i.c.PublishSubscribeChannel - Channel 'py-manager.errorChannel' has 1 subscriber(s).


# Created at 2025-08-05T10:40:13.378
Unexpected IOException with stream: 10:40:13.372 [34mINFO [0;39m [main] o.s.i.endpoint.EventDrivenConsumer - started bean '_org.springframework.integration.errorLogger'


# Created at 2025-08-05T10:40:13.469
Unexpected IOException with stream: 10:40:13.450 [34mINFO [0;39m [main] c.g.s.c.c.CommercialControllerTest - Started CommercialControllerTest in 67.798 seconds (JVM running for 71.4)


# Created at 2025-08-05T10:40:13.562
Unexpected IOException: 5,1,com.gl.service.commercial.controller.CommercialControllerTest,testExportList_MissingCSRFToken_ShouldReturnForbidden,null,null,null


# Created at 2025-08-05T10:40:14.214
Unexpected IOException with stream: 


# Created at 2025-08-05T10:40:14.216
Unexpected IOException with stream: MockHttpServletRequest:


# Created at 2025-08-05T10:40:14.216
Unexpected IOException with stream:       HTTP Method = POST


# Created at 2025-08-05T10:40:14.217
Unexpected IOException with stream:       Request URI = /commercial/export


# Created at 2025-08-05T10:40:14.218
Unexpected IOException with stream:        Parameters = {}


# Created at 2025-08-05T10:40:14.219
Unexpected IOException with stream:           Headers = [Content-Type:"application/json;charset=UTF-8", Content-Length:"106"]


# Created at 2025-08-05T10:40:14.221
Unexpected IOException with stream:              Body = {"id":null,"prop":null,"order":null,"searchCondition":"测试用户","shopName":"测试店铺","shopId":1}


# Created at 2025-08-05T10:40:14.222
Unexpected IOException with stream:     Session Attrs = {}


# Created at 2025-08-05T10:40:14.226
Unexpected IOException with stream: 


# Created at 2025-08-05T10:40:14.227
Unexpected IOException with stream: Handler:


# Created at 2025-08-05T10:40:14.228
Unexpected IOException with stream:              Type = com.gl.service.commercial.controller.CommercialController


# Created at 2025-08-05T10:40:14.229
Unexpected IOException with stream:            Method = com.gl.service.commercial.controller.CommercialController#exportList(CommercialDto, HttpServletResponse)


# Created at 2025-08-05T10:40:14.230
Unexpected IOException with stream: 


# Created at 2025-08-05T10:40:14.231
Unexpected IOException with stream: Async:


# Created at 2025-08-05T10:40:14.232
Unexpected IOException with stream:     Async started = false


# Created at 2025-08-05T10:40:14.232
Unexpected IOException with stream:      Async result = null


# Created at 2025-08-05T10:40:14.233
Unexpected IOException with stream: 


# Created at 2025-08-05T10:40:14.234
Unexpected IOException with stream: Resolved Exception:


# Created at 2025-08-05T10:40:14.235
Unexpected IOException with stream:              Type = null


# Created at 2025-08-05T10:40:14.235
Unexpected IOException with stream: 


# Created at 2025-08-05T10:40:14.236
Unexpected IOException with stream: ModelAndView:


# Created at 2025-08-05T10:40:14.237
Unexpected IOException with stream:         View name = null


# Created at 2025-08-05T10:40:14.238
Unexpected IOException with stream:              View = null


# Created at 2025-08-05T10:40:14.238
Unexpected IOException with stream:             Model = null


# Created at 2025-08-05T10:40:14.239
Unexpected IOException with stream: 


# Created at 2025-08-05T10:40:14.241
Unexpected IOException with stream: FlashMap:


# Created at 2025-08-05T10:40:14.243
Unexpected IOException with stream:        Attributes = null


# Created at 2025-08-05T10:40:14.244
Unexpected IOException with stream: 


# Created at 2025-08-05T10:40:14.245
Unexpected IOException with stream: MockHttpServletResponse:


# Created at 2025-08-05T10:40:14.247
Unexpected IOException with stream:            Status = 200


# Created at 2025-08-05T10:40:14.247
Unexpected IOException with stream:     Error message = null


# Created at 2025-08-05T10:40:14.248
Unexpected IOException with stream:           Headers = [X-Content-Type-Options:"nosniff", X-XSS-Protection:"1; mode=block", Cache-Control:"no-cache, no-store, max-age=0, must-revalidate", Pragma:"no-cache", Expires:"0"]


# Created at 2025-08-05T10:40:14.249
Unexpected IOException with stream:      Content type = null


# Created at 2025-08-05T10:40:14.250
Unexpected IOException with stream:              Body = 


# Created at 2025-08-05T10:40:14.250
Unexpected IOException with stream:     Forwarded URL = null


# Created at 2025-08-05T10:40:14.251
Unexpected IOException with stream:    Redirected URL = null


# Created at 2025-08-05T10:40:14.252
Unexpected IOException with stream:           Cookies = []


# Created at 2025-08-05T10:40:14.313
Unexpected IOException: 6,1,com.gl.service.commercial.controller.CommercialControllerTest,testExportList_MissingCSRFToken_ShouldReturnForbidden,null,null,null


# Created at 2025-08-05T10:40:14.319
Unexpected IOException: 2,1,org.apache.maven.surefire.junitplatform.JUnitPlatformProvider,com.gl.service.commercial.controller.CommercialControllerTest,null,null,null


