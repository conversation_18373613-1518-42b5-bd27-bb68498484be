package com.gl.service.template.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.service.opus.entity.Template;
import com.gl.service.opus.entity.TemplateType;
import com.gl.service.opus.repository.TemplateRepository;
import com.gl.service.template.vo.ExcelTemplate;
import com.gl.service.template.vo.TemplateVo;
import com.gl.service.template.vo.dto.TemplateDto;
import com.gl.system.vo.SysUserVo;
import com.gl.util.GetShopRefUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

/**
 * TemplateService单元测试类
 * 测试模板管理服务的所有业务逻辑
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("模板服务单元测试")
class TemplateServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private TemplateRepository templateRepository;

    @Mock
    private GetShopRefUtil shopRefUtil;

    @Mock
    private HttpServletResponse response;

    @Mock
    private ServletOutputStream outputStream;

    @InjectMocks
    private TemplateService templateService;

    private LoginUser mockLoginUser;
    private SysUserVo mockUser;
    private TemplateDto mockTemplateDto;
    private TemplateVo mockTemplateVo;
    private Template mockTemplate;
    private TemplateType mockTemplateType;

    @BeforeEach
    void setUp() {
        // 初始化模拟用户数据
        mockUser = new SysUserVo();
        mockUser.setId(1L);
        mockUser.setUserName("testUser");
        mockUser.setShopId(100L);

        mockLoginUser = new LoginUser();
        mockLoginUser.setUser(mockUser);

        // 初始化模拟DTO数据
        mockTemplateDto = new TemplateDto();
        mockTemplateDto.setShopId(100L);
        mockTemplateDto.setTemplateTypeId(1L);
        mockTemplateDto.setSearchCondition("测试");
        mockTemplateDto.setPageNumber(0);
        mockTemplateDto.setPageSize(10);

        // 初始化模拟VO数据
        mockTemplateVo = new TemplateVo();
        mockTemplateVo.setId(1L);
        mockTemplateVo.setTemplateTypeId(1L);
        mockTemplateVo.setTitle("测试模板");
        mockTemplateVo.setContent("测试内容");
        mockTemplateVo.setShopId(100L);

        // 初始化模拟实体数据
        mockTemplate = new Template();
        mockTemplate.setId(1L);
        mockTemplate.setTemplateTypeId(1L);
        mockTemplate.setTitle("测试模板");
        mockTemplate.setContent("测试内容");
        mockTemplate.setShopId(100L);
        mockTemplate.setDelStatus(0);
        mockTemplate.setCreateId(1L);
        mockTemplate.setCreateTime(new Date());

        // 初始化模拟模板类型数据
        mockTemplateType = new TemplateType();
        mockTemplateType.setId(1L);
        mockTemplateType.setName("测试类型");
        mockTemplateType.setDelStatus(0);
    }

    @Test
    @DisplayName("测试列表查询 - 成功获取模板列表")
    void testList_Success() {
        // Given
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(100L, 200L));

        List<TemplateVo> mockTemplateVos = Arrays.asList(mockTemplateVo);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(1L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(mockTemplateVos);

        // When
        Result result = templateService.list(mockTemplateDto, 1);

        // Then
        assertNotNull(result);
        assertEquals(10000, result.getCode());
        assertEquals("success", result.getMessage());

        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(1L, data.get("total"));
        assertEquals(mockTemplateVos, data.get("result"));

        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any());
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("测试列表查询 - 微信用户需要过滤时返回空列表")
    void testList_WxFilterNeeded_ReturnsEmptyList() {
        // Given
        when(shopRefUtil.isNeedWxFilter()).thenReturn(true);

        // When
        Result result = templateService.list(mockTemplateDto, 1);

        // Then
        assertNotNull(result);
        assertEquals(10000, result.getCode());

        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(0, data.get("total"));
        assertTrue(((List<?>) data.get("result")).isEmpty());

        verify(jdbcTemplate, never()).queryForObject(anyString(), eq(Integer.class), any(Object[].class));
    }

    @Test
    @DisplayName("测试列表查询 - 使用搜索条件")
    void testList_WithSearchCondition() {
        // Given
        mockTemplateDto.setSearchCondition("测试搜索");
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(100L));

        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(1L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(Arrays.asList(mockTemplateVo));

        // When
        Result result = templateService.list(mockTemplateDto, 1);

        // Then
        assertNotNull(result);
        assertEquals(10000, result.getCode());

        // 验证SQL包含搜索条件
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any());
    }

    @Test
    @DisplayName("测试列表查询 - 空DTO参数")
    void testList_NullDto() {
        // Given
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(100L));

        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(0L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(new ArrayList<>());

        // When
        Result result = templateService.list(null, 1);

        // Then
        assertNotNull(result);
        assertEquals(10000, result.getCode());

        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(0, ((Number) data.get("total")).intValue());
    }

    @Test
    @DisplayName("测试新增模板 - 成功新增")
    void testAddAndUpdate_AddSuccess() {
        // Given
        mockTemplateVo.setId(null); // 新增操作

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            when(templateRepository.save(any(Template.class))).thenReturn(mockTemplate);

            // When
            Result result = templateService.addAndUpdate(mockTemplateVo);

            // Then
            assertNotNull(result);
            assertEquals(10000, result.getCode());
            assertEquals("success", result.getMessage());

            verify(templateRepository, times(1)).save(any(Template.class));
        }
    }

    @Test
    @DisplayName("测试修改模板 - 成功修改已存在的模板")
    void testAddAndUpdate_UpdateExistingTemplate() {
        // Given
        mockTemplateVo.setId(1L); // 修改操作

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            when(templateRepository.findById(1L)).thenReturn(Optional.of(mockTemplate));
            when(templateRepository.save(any(Template.class))).thenReturn(mockTemplate);

            // When
            Result result = templateService.addAndUpdate(mockTemplateVo);

            // Then
            assertNotNull(result);
            assertEquals(10000, result.getCode());
            assertEquals("success", result.getMessage());

            verify(templateRepository, times(1)).findById(1L);
            verify(templateRepository, times(1)).save(any(Template.class));
        }
    }

    @Test
    @DisplayName("测试修改模板 - 模板不存在时创建新模板")
    void testAddAndUpdate_UpdateNonExistentTemplate() {
        // Given
        mockTemplateVo.setId(999L); // 不存在的ID

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            when(templateRepository.findById(999L)).thenReturn(Optional.empty());
            when(templateRepository.save(any(Template.class))).thenReturn(mockTemplate);

            // When
            Result result = templateService.addAndUpdate(mockTemplateVo);

            // Then
            assertNotNull(result);
            assertEquals(10000, result.getCode());
            assertEquals("success", result.getMessage());

            verify(templateRepository, times(1)).findById(999L);
            verify(templateRepository, times(1)).save(any(Template.class));
        }
    }

    @Test
    @DisplayName("测试新增/修改模板 - 参数为空时返回失败")
    void testAddAndUpdate_NullVo_ReturnsFail() {
        // Given
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When
            Result result = templateService.addAndUpdate(null);

            // Then
            assertNotNull(result);
            assertEquals(10001, result.getCode());
            assertEquals("数据不能为空", result.getMessage());

            verify(templateRepository, never()).save(any(Template.class));
        }
    }

    @Test
    @DisplayName("测试新增/修改模板 - 模板类型ID为空时返回失败")
    void testAddAndUpdate_NullTemplateTypeId_ReturnsFail() {
        // Given
        mockTemplateVo.setTemplateTypeId(null);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When
            Result result = templateService.addAndUpdate(mockTemplateVo);

            // Then
            assertNotNull(result);
            assertEquals(10001, result.getCode());
            assertEquals("模板类型不能为空", result.getMessage());

            verify(templateRepository, never()).save(any(Template.class));
        }
    }

    @Test
    @DisplayName("测试新增/修改模板 - 标题为空时返回失败")
    void testAddAndUpdate_BlankTitle_ReturnsFail() {
        // Given
        mockTemplateVo.setTitle("");

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When
            Result result = templateService.addAndUpdate(mockTemplateVo);

            // Then
            assertNotNull(result);
            assertEquals(10001, result.getCode());
            assertEquals("标题不能为空", result.getMessage());

            verify(templateRepository, never()).save(any(Template.class));
        }
    }

    @Test
    @DisplayName("测试新增/修改模板 - 内容为空时返回失败")
    void testAddAndUpdate_BlankContent_ReturnsFail() {
        // Given
        mockTemplateVo.setContent("");

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(mockLoginUser);

            // When
            Result result = templateService.addAndUpdate(mockTemplateVo);

            // Then
            assertNotNull(result);
            assertEquals(10001, result.getCode());
            assertEquals("内容不能为空", result.getMessage());

            verify(templateRepository, never()).save(any(Template.class));
        }
    }

    @Test
    @DisplayName("测试删除模板 - 成功删除")
    void testDelete_Success() {
        // Given
        mockTemplateDto.setIds(Arrays.asList(1L, 2L, 3L));

        when(templateRepository.updateDelStatusById(anyLong())).thenReturn(1);

        // When
        Result result = templateService.delete(mockTemplateDto);

        // Then
        assertNotNull(result);
        assertEquals(10000, result.getCode());
        assertEquals("success", result.getMessage());

        verify(templateRepository, times(3)).updateDelStatusById(anyLong());
        verify(templateRepository, times(1)).updateDelStatusById(1L);
        verify(templateRepository, times(1)).updateDelStatusById(2L);
        verify(templateRepository, times(1)).updateDelStatusById(3L);
    }

    @Test
    @DisplayName("测试删除模板 - 参数为空时返回失败")
    void testDelete_NullDto_ReturnsFail() {
        // When
        Result result = templateService.delete(null);

        // Then
        assertNotNull(result);
        assertEquals(10001, result.getCode());
        assertEquals("数据不能为空", result.getMessage());

        verify(templateRepository, never()).updateDelStatusById(anyLong());
    }

    @Test
    @DisplayName("测试删除模板 - ID列表为空时返回失败")
    void testDelete_EmptyIds_ReturnsFail() {
        // Given
        mockTemplateDto.setIds(new ArrayList<>());

        // When
        Result result = templateService.delete(mockTemplateDto);

        // Then
        assertNotNull(result);
        assertEquals(10001, result.getCode());
        assertEquals("模板id不能为空", result.getMessage());

        verify(templateRepository, never()).updateDelStatusById(anyLong());
    }

    @Test
    @DisplayName("测试删除模板 - ID列表为null时抛出空指针异常")
    void testDelete_NullIds_ThrowsNullPointerException() {
        // Given
        TemplateDto nullIdsDto = new TemplateDto();
        nullIdsDto.setIds(null);

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            templateService.delete(nullIdsDto);
        });

        verify(templateRepository, never()).updateDelStatusById(anyLong());
    }

    @Test
    @DisplayName("测试查找模板类型 - 成功获取模板类型列表")
    void testFindTemplateType_Success() {
        // Given
        List<TemplateType> mockTemplateTypes = Arrays.asList(mockTemplateType);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn((List<Object>) (List<?>) mockTemplateTypes);

        // When
        Result result = templateService.findTemplateType();

        // Then
        assertNotNull(result);
        assertEquals(10000, result.getCode());
        assertEquals("success", result.getMessage());
        assertEquals(mockTemplateTypes, result.getData());

        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class));
    }

    @Test
    @DisplayName("测试查找模板类型 - 返回空列表")
    void testFindTemplateType_EmptyList() {
        // Given
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(new ArrayList<TemplateType>());

        // When
        Result result = templateService.findTemplateType();

        // Then
        assertNotNull(result);
        assertEquals(10000, result.getCode());
        assertEquals("success", result.getMessage());
        assertTrue(((List<?>) result.getData()).isEmpty());

        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class));
    }

    @Test
    @DisplayName("测试导出列表 - 成功导出Excel")
    void testExportList_Success() throws IOException {
        // Given
        List<TemplateVo> mockTemplateVos = Arrays.asList(mockTemplateVo);
        mockTemplateVo.setCreateTime(new Date());
        mockTemplateVo.setCreateUserName("测试用户");
        mockTemplateVo.setTemplateTypeName("测试类型");

        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(100L));
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(1L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(mockTemplateVos);
        when(response.getOutputStream()).thenReturn(outputStream);

        // When
        templateService.exportList(mockTemplateDto, response);

        // Then
        verify(response, times(1)).setContentType("application/vnd.ms-excel");
        verify(response, times(1)).setCharacterEncoding("utf-8");
        verify(response, times(1)).setHeader(eq("Content-disposition"), contains("attachment;filename="));
        verify(response, times(1)).getOutputStream();
    }

    @Test
    @DisplayName("测试导出列表 - 空数据导出")
    void testExportList_EmptyData() throws IOException {
        // Given
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(100L));
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(0L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(new ArrayList<>());
        when(response.getOutputStream()).thenReturn(outputStream);

        // When
        templateService.exportList(mockTemplateDto, response);

        // Then
        verify(response, times(1)).setContentType("application/vnd.ms-excel");
        verify(response, times(1)).setCharacterEncoding("utf-8");
        verify(response, times(1)).setHeader(eq("Content-disposition"), contains("attachment;filename="));
        verify(response, times(1)).getOutputStream();
    }

    @Test
    @DisplayName("测试导出列表 - 处理IOException异常")
    void testExportList_IOException() throws IOException {
        // Given
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(100L));
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(0L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(new ArrayList<>());
        when(response.getOutputStream()).thenThrow(new IOException("测试异常"));

        // When & Then
        assertThrows(IOException.class, () -> {
            templateService.exportList(mockTemplateDto, response);
        });

        verify(response, times(1)).setContentType("application/vnd.ms-excel");
        verify(response, times(1)).setCharacterEncoding("utf-8");
        verify(response, times(1)).setHeader(eq("Content-disposition"), contains("attachment;filename="));
    }
}
