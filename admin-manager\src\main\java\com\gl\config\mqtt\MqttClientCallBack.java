package com.gl.config.mqtt;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

@Slf4j
@Component
public class MqttClientCallBack implements MqttCallback {

    @Value("${mqtt.clientId}")
    private String clientId;

    @Autowired
    private MqttClientConfig mqttClientConfig;

    /**
     * 智能解码MQTT消息载荷，尝试多种编码方式
     * 
     * @param payload 消息字节数组
     * @return 解码后的字符串
     */
    private String decodePayload(byte[] payload) {
        // 首先尝试UTF-8解码
        try {
            String utf8Result = new String(payload, StandardCharsets.UTF_8);
            // 检查是否包含UTF-8的替换字符，如果包含说明解码失败
            if (!utf8Result.contains("\uFFFD")) {
                return utf8Result;
            }
        } catch (Exception e) {
            log.debug("UTF-8解码失败", e);
        }

        // 尝试GBK解码
        try {
            String gbkResult = new String(payload, "GBK");
            return gbkResult;
        } catch (Exception e) {
            log.debug("GBK解码失败", e);
        }

        // 尝试GB2312解码
        try {
            String gb2312Result = new String(payload, "GB2312");
            return gb2312Result;
        } catch (Exception e) {
            log.debug("GB2312解码失败", e);
        }

        // 尝试ISO-8859-1解码（通常不会失败）
        try {
            String isoResult = new String(payload, "ISO-8859-1");
            return isoResult;
        } catch (Exception e) {
            log.debug("ISO-8859-1解码失败", e);
        }

        // 最后使用系统默认编码
        String defaultResult = new String(payload);
        log.warn("所有编码尝试都失败，使用系统默认编码，结果可能包含乱码: {}", defaultResult);
        return defaultResult;
    }

    /**
     * 与服务器断开的回调
     */
    @Override
    public void connectionLost(Throwable cause) {
        log.error(clientId + "client 与服务器断开连接！！异常类型: {}, 异常消息: {}",
                cause.getClass().getSimpleName(), cause.getMessage());
        log.error("连接断开堆栈信息: ", cause);
        log.info("连接断开，等待 SDK 自动重连，无需手动创建新连接");
    }

    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        String payload = decodePayload(message.getPayload());
        log.info(String.format("client 接收消息主题 : %s", topic));
        log.info(String.format("client 接收消息Qos : %d", message.getQos()));
        log.info(String.format("client 接收消息内容 : %s", payload));
        log.info(String.format("client 接收消息retained : %b", message.isRetained()));
    }

    /**
     * 消息发布成功的回调
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        IMqttAsyncClient client = token.getClient();
        String topic = Arrays.toString(token.getTopics());

        try {
            String message = JSONObject.toJSONString(token.getMessage());
            log.info("发布消息成功！ clientId: {}, topic : {}, Message: {}", client.getClientId(), topic, message);
        } catch (MqttException e) {
            throw new RuntimeException(e);
        }

    }
}
