package com.gl.commons.constant;

/**
 * Redis Key常量
 *
 * @date: 2019-12-04
 */
public class RedisKeyConstant {
    public final static String LOGIN_TOKEN = "login:token";
    // =================================系统参数相关KEY
    // Start================================ //
    /**
     * 系统参数HASH KEY
     */
    public final static String MZJ_PY_SYS_PARAMS = "MZJ_PY_SYS_PARAMS";

    /**
     * 小程序token 前缀
     */
    public static final java.lang.String SP_TOKEN = "py:token";

    /**
     * 系统参数-微信小程序appid
     */
    public final static String REDIS_SYS_PARAMS_OF_WX_SP_APPID = "wx.sp.appid";
    /**
     * 系统参数-微信小程序秘钥
     */
    public final static String REDIS_SYS_PARAMS_OF_WX_SP_SECRET = "wx.sp.secret";
    /**
     * 系统参数 播音率
     */
    public final static String SAMPLE_RATE = "sample_rate";

    /**
     * 语音合成token
     */
    public final static String VOICE_TOKEN = "voice:token";

    /**
     * 语音合成名称序号
     */
    public final static String VOICE_NUMBER = "voice:number";

    /**
     * 用户Id 配音查询计数
     */
    public final static String PY_REPEAT = "py:repeat:%s";

    /**
     * taskId 配音查询计数
     */
    public final static String PYLOCK_NUM = "py:lock:%s";

    // 设备指令相关 KEY
    /**
     * 设备指令发送锁
     */
    public final static String DEVICE_LOCK = "LOCK_%s";

    /**
     * 设备响应缓存
     */
    public final static String DEVICE_RESP = "RESP_%s_%s";

    /**
     * 设备操作日志缓存
     */
    public final static String DEVICE_OP_LOG = "OP_LOG_%s_%s";

    /**
     * 操作日志ID缓存
     */
    public final static String DEVICE_OP_LOG_ID = "device-operator-log-id-%s";
}