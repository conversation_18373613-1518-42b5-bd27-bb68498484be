package com.gl.config.snow;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SnowflakeConfig {

    @Value("${myapp.snowflake.worker-id}")
    private long workerId;

    @Value("${myapp.snowflake.datacenter-id}")
    private long datacenterId;

    @Bean
    public Snowflake snowflake() {
        // 第一个参数是 workerId，第二个参数是 datacenterId
        return IdUtil.getSnowflake(workerId, datacenterId);
    }
}