package com.gl.service.installationPackage.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.security.service.PermissionService;
import com.gl.service.installationPackage.service.InstallationPackageLogService;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * 安装包更新日志控制器测试配置类
 * 用于解决多个@SpringBootConfiguration冲突问题
 */
@TestConfiguration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class InstallationPackageLogTestConfig extends WebSecurityConfigurerAdapter {

    @MockBean
    private InstallationPackageLogService installationPackageLogService;

    @MockBean(name = "ps")
    private PermissionService permissionService;

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        return new ObjectMapper();
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .authorizeRequests()
            .anyRequest().permitAll();
    }
}
