package com.gl.service.deviceOperationLog.service;

import com.gl.commons.constant.RedisKeyConstant;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.service.device.repository.DeviceRepository;
import com.gl.service.deviceOperationLog.entity.DeviceOperationLog;
import com.gl.service.deviceOperationLog.repository.DeviceOperationLogRepository;
import com.gl.service.opus.entity.Device;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date: 2025/4/21
 * @description:
 */
@Slf4j
@Service
public class DeviceOperationLogService {

    @Autowired
    private DeviceOperationLogRepository deviceOperationLogRepository;
    @Autowired
    private DeviceRepository deviceRepository;

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    public Long sendSave(Long userId, Long deviceId, String deviceSn, String content) {
        Device device = deviceRepository.findBySn(deviceSn);
        if (device != null) {
            DeviceOperationLog deviceOperationLog = new DeviceOperationLog();
            deviceOperationLog.setUserId(userId);
            deviceOperationLog.setSendTime(new Date());
            deviceOperationLog.setDeviceId(device.getId());
            deviceOperationLog.setShopId(device.getShopId());
            deviceOperationLog.setContent(content);
            deviceOperationLog.setStatus(4);
            deviceOperationLogRepository.save(deviceOperationLog);

            Long id = deviceOperationLog.getId();
            redisTemplate.opsForValue().set(String.format(RedisKeyConstant.DEVICE_OP_LOG_ID, id), String.valueOf(id),
                    10, TimeUnit.SECONDS);
            return id;
        }

        return null;
    }

    public void update(Long id, String responseContent) {

        Optional<DeviceOperationLog> operationLog = deviceOperationLogRepository.findById(id);

        if (!operationLog.isPresent()) {
            log.error("没有找到存储的操作数据 id:{},responseContent:{}", id, responseContent);
            return;
        }

        DeviceOperationLog entity = operationLog.get();
        entity.setResponseContent(responseContent);
        entity.setResponseTime(new Date());
        entity.setStatus(1);
        deviceOperationLogRepository.save(entity);
    }

    public void callBackLog(String deviceSn, String message) {
    }
}
