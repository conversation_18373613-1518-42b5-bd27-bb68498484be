package com.gl.service.installationPackage.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.security.service.PermissionService;
import com.gl.framework.web.response.Result;
import com.gl.service.installationPackage.service.InstallationPackageLogService;
import com.gl.service.installationPackage.vo.installationPackageLog.InstallationPackageLogVo;
import com.gl.service.installationPackage.vo.installationPackageLog.dto.InstallationPackageLogDelDto;
import com.gl.service.installationPackage.vo.installationPackageLog.dto.InstallationPackageLogDto;
import com.gl.service.installationPackage.vo.installationPackageLog.dto.InstallationPackageLogRenewalDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * InstallationPackageLogController单元测试类
 * 测试安装包更新日志控制器的所有HTTP请求处理
 */
@WebMvcTest(controllers = InstallationPackageLogController.class)
@DisplayName("安装包更新日志控制器测试")
@TestPropertySource(properties = {
                "spring.main.allow-bean-definition-overriding=true"
})
@TestPropertySource(locations = "classpath:application-test.yml")
@ContextConfiguration(classes = { InstallationPackageLogController.class, InstallationPackageLogTestConfig.class })
class InstallationPackageLogControllerTest {

        @Autowired
        private MockMvc mockMvc;

        @Autowired
        private InstallationPackageLogService installationPackageLogService;

        @Autowired
        private PermissionService permissionService;

        @Autowired
        private ObjectMapper objectMapper;

        private InstallationPackageLogDto testDto;
        private InstallationPackageLogDelDto testDelDto;
        private InstallationPackageLogRenewalDto testRenewalDto;
        private Result successResult;
        private Result failResult;
        private List<InstallationPackageLogVo> testLogVoList;

        @BeforeEach
        void setUp() {
                // 重置所有mock
                reset(installationPackageLogService, permissionService);

                // 配置权限服务mock - 默认允许所有权限
                when(permissionService.hasPermi("dub:installationPackageLog:list")).thenReturn(true);
                when(permissionService.hasPermi("dub:installationPackageLog:delete")).thenReturn(true);
                when(permissionService.hasPermi("dub:installationPackageLog:renewal")).thenReturn(true);

                // 初始化测试数据
                testDto = new InstallationPackageLogDto();
                testDto.setSearchCondition("test");

                testDelDto = new InstallationPackageLogDelDto();
                testDelDto.setIds(Arrays.asList(1L, 2L, 3L));

                testRenewalDto = new InstallationPackageLogRenewalDto();
                testRenewalDto.setInstallationPackageId(100L);
                testRenewalDto.setDeviceIdList(Arrays.asList(1L, 2L, 3L));

                // 创建测试VO数据
                testLogVoList = new ArrayList<>();
                InstallationPackageLogVo vo1 = new InstallationPackageLogVo();
                vo1.setId(1L);
                vo1.setShopName("测试店铺1");
                vo1.setDeviceName("测试设备1");
                vo1.setSn("SN001");
                vo1.setVersionName("v1.0.0");
                vo1.setCreateTime(new Date());
                vo1.setStatus(1);
                testLogVoList.add(vo1);

                // 创建成功和失败的Result对象
                successResult = Result.success();
                successResult.addData("total", 2L);
                successResult.addData("result", testLogVoList);

                failResult = Result.fail("操作失败");
        }

        @Test
        @DisplayName("测试GET /installationPackageLog - 正常查询列表")
        @WithMockUser(authorities = "dub:installationPackageLog:list")
        void testList_WithValidParameters_ShouldReturnSuccessResult() throws Exception {
                // Given
                when(installationPackageLogService.list(any(InstallationPackageLogDto.class), eq(1)))
                                .thenReturn(successResult);

                // When & Then
                mockMvc.perform(get("/installationPackageLog")
                                .param("searchCondition", "test")
                                .param("beginTime", "2024-01-01 00:00:00")
                                .param("endTime", "2024-12-31 23:59:59")
                                .param("pageSize", "10")
                                .param("pageNumber", "0")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10000))
                                .andExpect(jsonPath("$.message").value("success"))
                                .andExpect(jsonPath("$.data.total").value(2))
                                .andExpect(jsonPath("$.data.result").isArray())
                                .andExpect(jsonPath("$.data.result[0].id").value(1))
                                .andExpect(jsonPath("$.data.result[0].shopName").value("测试店铺1"))
                                .andExpect(jsonPath("$.data.result[0].deviceName").value("测试设备1"))
                                .andExpect(jsonPath("$.data.result[0].sn").value("SN001"))
                                .andExpect(jsonPath("$.data.result[0].versionName").value("v1.0.0"))
                                .andExpect(jsonPath("$.data.result[0].status").value(1));

                // 验证Service方法被正确调用
                verify(installationPackageLogService, times(1))
                                .list(any(InstallationPackageLogDto.class), eq(1));
        }

        @Test
        @DisplayName("测试GET /installationPackageLog - 无搜索条件查询")
        @WithMockUser(authorities = "dub:installationPackageLog:list")
        void testList_WithoutSearchCondition_ShouldReturnSuccessResult() throws Exception {
                // Given
                when(installationPackageLogService.list(any(InstallationPackageLogDto.class), eq(1)))
                                .thenReturn(successResult);

                // When & Then
                mockMvc.perform(get("/installationPackageLog")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10000))
                                .andExpect(jsonPath("$.message").value("success"));

                // 验证Service方法被调用
                verify(installationPackageLogService, times(1))
                                .list(any(InstallationPackageLogDto.class), eq(1));
        }

        @Test
        @DisplayName("测试GET /installationPackageLog - 无权限访问")
        @WithMockUser(authorities = "other:permission")
        void testList_WithoutPermission_ShouldReturnForbidden() throws Exception {
                // Given - 配置权限服务返回false，表示用户没有权限
                when(permissionService.hasPermi("dub:installationPackageLog:list")).thenReturn(false);

                // When & Then - Spring Security会直接返回403状态码
                mockMvc.perform(get("/installationPackageLog")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isForbidden()); // 期望403状态码

                // 验证Service方法未被调用
                verify(installationPackageLogService, never())
                                .list(any(InstallationPackageLogDto.class), anyInt());
        }

        @Test
        @DisplayName("测试GET /installationPackageLog - Service返回失败结果")
        @WithMockUser(authorities = "dub:installationPackageLog:list")
        void testList_WithServiceFailure_ShouldReturnFailResult() throws Exception {
                // Given
                when(installationPackageLogService.list(any(InstallationPackageLogDto.class), eq(1)))
                                .thenReturn(failResult);

                // When & Then
                mockMvc.perform(get("/installationPackageLog")
                                .param("searchCondition", "test")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10001))
                                .andExpect(jsonPath("$.message").value("操作失败"));

                // 验证Service方法被调用
                verify(installationPackageLogService, times(1))
                                .list(any(InstallationPackageLogDto.class), eq(1));
        }

        @Test
        @DisplayName("测试DELETE /installationPackageLog - 正常删除")
        @WithMockUser(authorities = "dub:installationPackageLog:delete")
        void testDelete_WithValidIds_ShouldReturnSuccessResult() throws Exception {
                // Given
                when(installationPackageLogService.delete(any(InstallationPackageLogDelDto.class)))
                                .thenReturn(Result.success());

                // When & Then
                mockMvc.perform(delete("/installationPackageLog")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDelDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10000))
                                .andExpect(jsonPath("$.message").value("success"));

                // 验证Service方法被正确调用
                verify(installationPackageLogService, times(1))
                                .delete(any(InstallationPackageLogDelDto.class));
        }

        @Test
        @DisplayName("测试DELETE /installationPackageLog - 无权限删除")
        @WithMockUser(authorities = "other:permission")
        void testDelete_WithoutPermission_ShouldReturnForbidden() throws Exception {
                // Given - 配置权限服务返回false，表示用户没有权限
                when(permissionService.hasPermi("dub:installationPackageLog:delete")).thenReturn(false);

                // When & Then - Spring Security会直接返回403状态码
                mockMvc.perform(delete("/installationPackageLog")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDelDto)))
                                .andDo(print())
                                .andExpect(status().isForbidden()); // 期望403状态码

                // 验证Service方法未被调用
                verify(installationPackageLogService, never())
                                .delete(any(InstallationPackageLogDelDto.class));
        }

        @Test
        @DisplayName("测试DELETE /installationPackageLog - Service返回失败结果")
        @WithMockUser(authorities = "dub:installationPackageLog:delete")
        void testDelete_WithServiceFailure_ShouldReturnFailResult() throws Exception {
                // Given
                when(installationPackageLogService.delete(any(InstallationPackageLogDelDto.class)))
                                .thenReturn(Result.fail("删除失败"));

                // When & Then
                mockMvc.perform(delete("/installationPackageLog")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testDelDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10001))
                                .andExpect(jsonPath("$.message").value("删除失败"));

                // 验证Service方法被调用
                verify(installationPackageLogService, times(1))
                                .delete(any(InstallationPackageLogDelDto.class));
        }

        @Test
        @DisplayName("测试DELETE /installationPackageLog - 请求体为空")
        @WithMockUser(authorities = "dub:installationPackageLog:delete")
        void testDelete_WithEmptyRequestBody_ShouldReturnBadRequest() throws Exception {
                // When & Then - 空请求体会被Spring MVC处理，返回400状态码
                mockMvc.perform(delete("/installationPackageLog")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(""))
                                .andDo(print())
                                .andExpect(status().isBadRequest()); // 期望400状态码

                // 验证Service方法未被调用（空字符串不会触发Service调用）
                verify(installationPackageLogService, never())
                                .delete(any());
        }

        @Test
        @DisplayName("测试POST /installationPackageLog/renewal - 正常更新安装包")
        @WithMockUser(authorities = "dub:installationPackageLog:renewal")
        void testRenewal_WithValidData_ShouldReturnSuccessResult() throws Exception {
                // Given
                when(installationPackageLogService.renewal(any(InstallationPackageLogRenewalDto.class)))
                                .thenReturn(Result.success());

                // When & Then
                mockMvc.perform(post("/installationPackageLog/renewal")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testRenewalDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10000))
                                .andExpect(jsonPath("$.message").value("success"));

                // 验证Service方法被正确调用
                verify(installationPackageLogService, times(1))
                                .renewal(any(InstallationPackageLogRenewalDto.class));
        }

        @Test
        @DisplayName("测试POST /installationPackageLog/renewal - 无权限更新")
        @WithMockUser(authorities = "other:permission")
        void testRenewal_WithoutPermission_ShouldReturnForbidden() throws Exception {
                // Given - 配置权限服务返回false，表示用户没有权限
                when(permissionService.hasPermi("dub:installationPackageLog:renewal")).thenReturn(false);

                // When & Then - Spring Security会直接返回403状态码
                mockMvc.perform(post("/installationPackageLog/renewal")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testRenewalDto)))
                                .andDo(print())
                                .andExpect(status().isForbidden()); // 期望403状态码

                // 验证Service方法未被调用
                verify(installationPackageLogService, never())
                                .renewal(any(InstallationPackageLogRenewalDto.class));
        }

        @Test
        @DisplayName("测试POST /installationPackageLog/renewal - Service返回失败结果")
        @WithMockUser(authorities = "dub:installationPackageLog:renewal")
        void testRenewal_WithServiceFailure_ShouldReturnFailResult() throws Exception {
                // Given
                when(installationPackageLogService.renewal(any(InstallationPackageLogRenewalDto.class)))
                                .thenReturn(Result.fail("更新失败"));

                // When & Then
                mockMvc.perform(post("/installationPackageLog/renewal")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(testRenewalDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value(10001))
                                .andExpect(jsonPath("$.message").value("更新失败"));

                // 验证Service方法被调用
                verify(installationPackageLogService, times(1))
                                .renewal(any(InstallationPackageLogRenewalDto.class));
        }

        @Test
        @DisplayName("测试POST /installationPackageLog/renewal - 请求体为空")
        @WithMockUser(authorities = "dub:installationPackageLog:renewal")
        void testRenewal_WithEmptyRequestBody_ShouldReturnBadRequest() throws Exception {
                // When & Then - 空请求体会被Spring MVC处理，返回400状态码
                mockMvc.perform(post("/installationPackageLog/renewal")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(""))
                                .andDo(print())
                                .andExpect(status().isBadRequest()); // 期望400状态码

                // 验证Service方法未被调用（空字符串不会触发Service调用）
                verify(installationPackageLogService, never())
                                .renewal(any());
        }

        @Test
        @DisplayName("测试POST /installationPackageLog/renewal - JSON格式错误")
        @WithMockUser(authorities = "dub:installationPackageLog:renewal")
        void testRenewal_WithInvalidJson_ShouldReturnBadRequest() throws Exception {
                // When & Then - JSON格式错误会被Spring MVC处理，返回400状态码
                mockMvc.perform(post("/installationPackageLog/renewal")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("{invalid json}"))
                                .andDo(print())
                                .andExpect(status().isBadRequest()); // 期望400状态码

                // 验证Service方法未被调用
                verify(installationPackageLogService, never())
                                .renewal(any(InstallationPackageLogRenewalDto.class));
        }

        @Test
        @DisplayName("测试DELETE /installationPackageLog - JSON格式错误")
        @WithMockUser(authorities = "dub:installationPackageLog:delete")
        void testDelete_WithInvalidJson_ShouldReturnBadRequest() throws Exception {
                // When & Then - JSON格式错误会被Spring MVC处理，返回400状态码
                mockMvc.perform(delete("/installationPackageLog")
                                .with(csrf())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("{invalid json}"))
                                .andDo(print())
                                .andExpect(status().isBadRequest()); // 期望400状态码

                // 验证Service方法未被调用
                verify(installationPackageLogService, never())
                                .delete(any(InstallationPackageLogDelDto.class));
        }

        @Test
        @DisplayName("测试GET /installationPackageLog - Service抛出异常")
        @WithMockUser(authorities = "dub:installationPackageLog:list")
        void testList_WithServiceException_ShouldHandleException() throws Exception {
                // Given
                when(installationPackageLogService.list(any(InstallationPackageLogDto.class), eq(1)))
                                .thenThrow(new RuntimeException("数据库连接异常"));

                // When & Then - 期望异常被处理，可能返回500或者被全局异常处理器处理
                try {
                        mockMvc.perform(get("/installationPackageLog")
                                        .param("searchCondition", "test")
                                        .contentType(MediaType.APPLICATION_JSON))
                                        .andDo(print());
                        // 如果没有抛出异常，说明被全局异常处理器处理了
                } catch (Exception e) {
                        // 如果抛出异常，验证是预期的异常类型
                        assert e.getCause() instanceof RuntimeException;
                        assert e.getCause().getMessage().contains("数据库连接异常");
                }

                // 验证Service方法被调用
                verify(installationPackageLogService, times(1))
                                .list(any(InstallationPackageLogDto.class), eq(1));
        }

        @Test
        @DisplayName("测试DELETE /installationPackageLog - Service抛出异常")
        @WithMockUser(authorities = "dub:installationPackageLog:delete")
        void testDelete_WithServiceException_ShouldHandleException() throws Exception {
                // Given
                when(installationPackageLogService.delete(any(InstallationPackageLogDelDto.class)))
                                .thenThrow(new RuntimeException("数据库连接异常"));

                // When & Then - 期望异常被处理
                try {
                        mockMvc.perform(delete("/installationPackageLog")
                                        .with(csrf())
                                        .contentType(MediaType.APPLICATION_JSON)
                                        .content(objectMapper.writeValueAsString(testDelDto)))
                                        .andDo(print());
                        // 如果没有抛出异常，说明被全局异常处理器处理了
                } catch (Exception e) {
                        // 如果抛出异常，验证是预期的异常类型
                        assert e.getCause() instanceof RuntimeException;
                        assert e.getCause().getMessage().contains("数据库连接异常");
                }

                // 验证Service方法被调用
                verify(installationPackageLogService, times(1))
                                .delete(any(InstallationPackageLogDelDto.class));
        }

        @Test
        @DisplayName("测试POST /installationPackageLog/renewal - Service抛出异常")
        @WithMockUser(authorities = "dub:installationPackageLog:renewal")
        void testRenewal_WithServiceException_ShouldHandleException() throws Exception {
                // Given
                when(installationPackageLogService.renewal(any(InstallationPackageLogRenewalDto.class)))
                                .thenThrow(new RuntimeException("数据库连接异常"));

                // When & Then - 期望异常被处理
                try {
                        mockMvc.perform(post("/installationPackageLog/renewal")
                                        .with(csrf())
                                        .contentType(MediaType.APPLICATION_JSON)
                                        .content(objectMapper.writeValueAsString(testRenewalDto)))
                                        .andDo(print());
                        // 如果没有抛出异常，说明被全局异常处理器处理了
                } catch (Exception e) {
                        // 如果抛出异常，验证是预期的异常类型
                        assert e.getCause() instanceof RuntimeException;
                        assert e.getCause().getMessage().contains("数据库连接异常");
                }

                // 验证Service方法被调用
                verify(installationPackageLogService, times(1))
                                .renewal(any(InstallationPackageLogRenewalDto.class));
        }
}
