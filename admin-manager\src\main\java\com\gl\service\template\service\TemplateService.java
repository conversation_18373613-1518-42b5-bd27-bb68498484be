package com.gl.service.template.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.sql.SqlUtils;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.service.opus.entity.Template;
import com.gl.service.opus.entity.TemplateType;
import com.gl.service.opus.repository.TemplateRepository;
import com.gl.service.template.vo.ExcelTemplate;
import com.gl.service.template.vo.TemplateVo;
import com.gl.service.template.vo.dto.TemplateDto;
import com.gl.system.vo.SysUserVo;
import com.gl.util.GetShopRefUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: duanjinze
 * @date: 2022/11/11 10:28
 * @version: 1.0
 */
@Service
@Slf4j
public class TemplateService {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private TemplateRepository templateRepository;
    @Autowired
    private GetShopRefUtil shopRefUtil;

    public Result list(TemplateDto dto, Integer exportType) {
        Result result = Result.success();
        if (shopRefUtil.isNeedWxFilter()) {
            result.addData("total", 0);
            result.addData("result", new ArrayList<>());
            return result;
        }
        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();
        if (dto != null) {
            if (dto.getShopId() != null) {
                where.append(" and s.`id` = ? ");
                args.add(dto.getShopId());
            }
            if (dto.getTemplateTypeId() != null) {
                where.append(" and dt.template_type_id = ? ");
                args.add(dto.getTemplateTypeId());
            }
            if (StringUtils.isNotBlank(dto.getSearchCondition())) {
                where.append(" and (dt.title like ? or dt.content like ? ) ");
                args.add("%" + dto.getSearchCondition() + "%");
                args.add("%" + dto.getSearchCondition() + "%");
            }
        }
        List<Long> shopRef = shopRefUtil.getShopRef();
        if (CollUtil.isNotEmpty(shopRef)) {
            where.append(String.format(" AND s.id in %s", SqlUtils.foreach("(", ")", ",", shopRef)));
        }

        String sql = "SELECT dt.id,dt.template_type_id,dtt.name AS templateTypeName,dt.title,dt.content," +
                "su.user_name as createUserName,dt.create_time,s.shop_name,s.id shopId " +
                "FROM dub_template dt \n" +
                "LEFT JOIN dub_template_type dtt\n" +
                "ON dtt.id = dt.template_type_id\n" +
                "LEFT JOIN sys_user su \n" +
                "ON su.id = dt.create_id\n" +
                "LEFT JOIN dub_shop s ON s.id = dt.shop_id \n" +
                "WHERE dt.del_status != 1 and dtt.del_status != 1 \n ";

        Long count = jdbcTemplate.queryForObject("select count(1) from (" + sql + where + ") t", Long.class,
                args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result", null);
            return result;
        }
        where.append(" order by dt.create_time DESC ");
        if (exportType == 1) {
            where.append(" LIMIT ? OFFSET ? ");
            assert dto != null;
            args.add(dto.getPageSize());
            args.add(dto.getPageNumber() * dto.getPageSize());
        }
        List<TemplateVo> templateVos = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(TemplateVo.class),
                args.toArray());
        result.addData("total", count);
        result.addData("result", templateVos);
        return result;
    }

    public void exportList(TemplateDto dto, HttpServletResponse response) throws IOException {
        Result list = list(dto, 2);
        HashMap hashMap = JSONObject.parseObject(JSONObject.toJSONString(list.getData()), HashMap.class);
        Object result = hashMap.get("result");
        List<ExcelTemplate> excelTemplates = null;
        if (result != null) {
            List<TemplateVo> templateVos = JSON.parseArray(JSON.toJSONString(result), TemplateVo.class);
            excelTemplates = templateVos.stream().map(item -> {
                ExcelTemplate excelTemplate = new ExcelTemplate();
                excelTemplate.setTemplateTypeName(item.getTemplateTypeName());
                excelTemplate.setTitle(item.getTitle());
                excelTemplate.setContent(item.getContent());
                excelTemplate.setCreateUserName(item.getCreateUserName());
                excelTemplate.setCreateTime(item.getCreateTime() == null ? ""
                        : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(item.getCreateTime()));
                return excelTemplate;
            }).collect(Collectors.toList());
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = "模板管理_" + System.currentTimeMillis();
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ExcelTypeEnum.XLSX.getValue());
        EasyExcel.write(response.getOutputStream(), ExcelTemplate.class).sheet("模板管理").doWrite(excelTemplates);
        // 导出excel
        log.info("模板管理导出end");
    }

    public Result addAndUpdate(TemplateVo vo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        if (vo == null) {
            return Result.fail("数据不能为空");
        }
        if (vo.getTemplateTypeId() == null) {
            return Result.fail("模板类型不能为空");
        }
        if (StringUtils.isBlank(vo.getTitle())) {
            return Result.fail("标题不能为空");
        }
        if (StringUtils.isBlank(vo.getContent())) {
            return Result.fail("内容不能为空");
        }

        if (vo.getId() == null) {
            // 新增
            Template template = new Template();
            template.setShopId(vo.getShopId());
            template.setTemplateTypeId(vo.getTemplateTypeId());
            template.setTitle(vo.getTitle());
            template.setContent(vo.getContent());
            template.setDelStatus(0);
            template.setCreateId(user.getId());
            template.setCreateTime(new Date());
            templateRepository.save(template);
        } else {
            Optional<Template> byId = templateRepository.findById(vo.getId());
            if (byId.isPresent()) {
                // 修改
                Template template = byId.get();
                template.setTemplateTypeId(vo.getTemplateTypeId());
                template.setTitle(vo.getTitle());
                template.setContent(vo.getContent());
                template.setShopId(vo.getShopId());
                templateRepository.save(template);
            } else {
                Template template = new Template();
                template.setShopId(user.getShopId());
                template.setTemplateTypeId(vo.getTemplateTypeId());
                template.setTitle(vo.getTitle());
                template.setContent(vo.getContent());
                template.setDelStatus(0);
                template.setCreateId(user.getId());
                template.setCreateTime(new Date());
                templateRepository.save(template);
            }
        }

        return Result.success();
    }

    public Result delete(TemplateDto dto) {
        if (dto == null) {
            return Result.fail("数据不能为空");
        }
        if (dto.getIds().isEmpty()) {
            return Result.fail("模板id不能为空");
        }
        for (Long id : dto.getIds()) {
            templateRepository.updateDelStatusById(id);
        }
        return Result.success();
    }

    public Result findTemplateType() {
        String sql = "select id,name from dub_template_type where del_status != 1 ";
        List<TemplateType> templateTypes = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(TemplateType.class));
        return Result.success(templateTypes);
    }
}
