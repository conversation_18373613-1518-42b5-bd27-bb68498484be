package com.gl.service.opus.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gl.commons.enums.DubbingTypeEnum;
import com.gl.compound.entity.SpeechSynthesizerDto;
import com.gl.config.mqtt.MqttClientConfig;
import com.gl.config.snow.IdGeneratorService;
import com.gl.framework.common.util.SecurityUtils;
import com.gl.framework.common.util.sql.SqlUtils;
import com.gl.framework.security.LoginUser;
import com.gl.framework.web.response.Result;
import com.gl.redis.Constant;
import com.gl.redis.RedisService;
import com.gl.service.device.repository.DeviceRepository;
import com.gl.service.device.vo.BackGroundMusicTypeVo;
import com.gl.service.device.vo.TemplateTypeVo;
import com.gl.service.opus.compound.compound.DubbingFactory;
import com.gl.service.opus.entity.*;
import com.gl.service.opus.repository.*;
import com.gl.service.opus.utils.FfmpegUtil;
import com.gl.service.opus.utils.FileRenameUtils;
import com.gl.service.opus.utils.FileUtil;
import com.gl.service.opus.vo.*;
import com.gl.service.opus.vo.dto.WorkDto;
import com.gl.service.oss.service.OSSService;
import com.gl.service.shop.vo.ShopSelectListVo;
import com.gl.system.vo.SysUserVo;
import com.gl.util.GetShopRefUtil;
import com.gl.util.mqtt.MqttSample;
import com.gl.wechat.entity.WechatUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jaudiotagger.audio.AudioFileIO;
import org.jaudiotagger.audio.mp3.MP3AudioHeader;
import org.jaudiotagger.audio.mp3.MP3File;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 作品管理
 *
 * @author: duanjinze
 * @date: 2022/11/10 13:46
 * @version: 1.0
 */
@Service
@Slf4j
public class WorkService {

    private static final String countSql = "SELECT COUNT(1) ";

    @Value("${ali.oss.downFile.tempdir}")
    private String appUrl;
    @Value("${ali.oss.bucket.url}")
    private String ossUrl;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private VoiceWorkRepository voiceWorkRepository;
    @Autowired
    private VoicePacketRepository voicePacketRepository;
    @Autowired
    private TemplateRepository templateRepository;
    @Autowired
    private OSSService ossService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private GetShopRefUtil shopRefUtil;
    @Autowired
    private MqttSample mqttSample;
    @Autowired
    private DeviceRepository deviceRepository;
    @Autowired
    private MqttClientConfig mqttClientConfig;

    @Autowired
    private IdGeneratorService idGeneratorService;
    @Autowired
    private AnchorRepository anchorRepository;

    @Resource
    private DubbingFactory dubbingFactory;

    @Resource
    private LongAnchorRepository longAnchorRepository;

    @Resource
    private AiStyleRepository aiStyleRepository;
    @Resource
    private AiClassRepository aiClassRepository;
    @Resource
    private AiLanguageRepository aiLanguageRepository;
    @Resource
    private UserBackgroundMusicRepository userBackgroundMusicRepository;

    /**
     * @param dto
     * @param exportType 导入标识 1不导入 2导入
     * @return
     */
    public Result list(WorkDto dto, Integer exportType) {
        Result result = Result.success();
        if (shopRefUtil.isNeedWxFilter()) {
            result.addData("total", 0);
            result.addData("result", new ArrayList<>());
            return result;
        }
        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();
        if (dto != null) {
            if (dto.getAnchorId() != null) {
                where.append(" and a.id = ? ");
                args.add(dto.getAnchorId());
            }
            if (dto.getShopId() != null) {
                where.append(" and s.id = ? ");
                args.add(dto.getShopId());
            }

            if (StringUtils.isNotBlank(dto.getBeginTime())) {
                where.append(" and vw.create_time >= ? ");
                args.add(dto.getBeginTime());
            }
            if (StringUtils.isNotBlank(dto.getEndTime())) {
                where.append(" and vw.create_time <= ? ");
                args.add(dto.getEndTime());
            }
            //【作品标题、作品文字、背景音乐、用户昵称、用户手机】
            if (StringUtils.isNotBlank(dto.getSearchCondition())) {
                where.append(" and (vw.title like ? or vw.content like ? or bm.name like ? or wu.nickname like ? or wu.phone like ? ) ");
                args.add("%" + dto.getSearchCondition() + "%");
                args.add("%" + dto.getSearchCondition() + "%");
                args.add("%" + dto.getSearchCondition() + "%");
                args.add("%" + dto.getSearchCondition() + "%");
                args.add("%" + dto.getSearchCondition() + "%");
            }
        }
        List<Long> shopRef = shopRefUtil.getShopRef();
        if (CollUtil.isNotEmpty(shopRef)) {
            where.append(String.format(" AND s.id in %s", SqlUtils.foreach("(", ")", ",", shopRef)));
        }
        String dataSql = "SELECT vw.id,vw.title,vw.content, " + "vp.file_url as fileUrl,a.name as anchorName,vw.voice_time,vw.speed,"
                + "bm.name as musicName,wu.nickname ,wu.phone,vw.create_time," + "a.id as anchorId, vp.id as voiceId,vw.volume, vw.pitch, "
                + "vw.background_music_volume as backgroundMusicVolume, vw.background_music_id as backgroundMusicId,"
                + "vw.sample_rate as sampleRate, vw.user_id as userId, vw.del_status as delStatus,s.shop_name,s.id as shopId ";

        String sql = "FROM dub_voice_work vw\n" + "LEFT JOIN dub_voice_packet vp \n" + "ON vp.voice_work_id = vw.id\n"
                + "LEFT JOIN dub_anchor a \n" + "ON a.id = vw.anchor_id\n" + "LEFT JOIN dub_background_music bm\n"
                + "ON bm.id = vw.background_music_id\n" + "LEFT JOIN dub_wechat_user wu \n" + "ON wu.id = vw.user_id\n"
                + "LEFT JOIN dub_shop s on s.id = vw.shop_id \n" + "WHERE vw.del_status != 1\n";
        Long count = jdbcTemplate.queryForObject(countSql + sql + where, Long.class, args.toArray());
        if (count == null || count == 0) {
            result.addData("total", 0);
            result.addData("result", null);
            return result;
        }
        where.append(" order by vw.create_time DESC ");
        if (exportType == 1) {
            where.append(" LIMIT ? OFFSET ? ");
            assert dto != null;
            args.add(dto.getPageSize());
            args.add(dto.getPageNumber() * dto.getPageSize());
        }
        List<VoiceWorkVo> voiceWorkVos = jdbcTemplate.query(dataSql + sql + where, new BeanPropertyRowMapper<>(VoiceWorkVo.class), args.toArray());
        result.addData("total", count);
        result.addData("result", voiceWorkVos);
        return result;
    }

    public Result add(VoiceWorkVo vo) {

        if (vo == null) {
            return Result.fail("数据不能为空");
        }
        if (StringUtils.isBlank(vo.getTitle())) {
            return Result.fail("作品标题不能为空");
        }
        if (StringUtils.isBlank(vo.getContent())) {
            return Result.fail("作品文字内容不能为空");
        }
        if (vo.getAnchorId() == null) {
            return Result.fail("主播id不能为空");
        }
        File file = ossService.getObjectFile(null, vo.getFileUrl());
        ossService.deleteObject(null, vo.getFileUrl());
        String url = ossService.putFileToName(null, file, "voice", UUID.randomUUID().toString());
        //生成语音包
        VoicePacket voicePacket = new VoicePacket();
        voicePacket.setShopId(vo.getShopId());
        voicePacket.setName(vo.getTitle());
        voicePacket.setVoiceTime(vo.getVoiceTime());
        voicePacket.setFileUrl(url);
        VoicePacket saveVoicePacket = voicePacketRepository.save(voicePacket);
        //生成作品
        VoiceWork voiceWork = new VoiceWork();
        voiceWork.setTitle(vo.getTitle());
        voiceWork.setContent(vo.getContent());
        voiceWork.setShopId(vo.getShopId());
        voiceWork.setVoiceId(saveVoicePacket.getId());
        voiceWork.setAnchorId(vo.getAnchorId());
        voiceWork.setVoiceTime(vo.getVoiceTime());
        voiceWork.setSpeed(vo.getSpeed());
        voiceWork.setVolume(vo.getVolume());
        voiceWork.setPitch(vo.getPitch());
        voiceWork.setBackgroundMusicVolume(vo.getBackgroundMusicVolume());
        if (vo.getBackgroundMusicId() != null) {
            voiceWork.setBackgroundMusicId(vo.getBackgroundMusicId());
        }
        voiceWork.setUserId(vo.getUserId());
        voiceWork.setDelStatus(0);
        voiceWork.setCreateTime(new Date());
        voiceWork.setSampleRate(24000);
        VoiceWork saveVoiceWork = voiceWorkRepository.save(voiceWork);
        saveVoicePacket.setVoiceWorkId(saveVoiceWork.getId());
        voicePacketRepository.save(saveVoicePacket);
        return Result.success();
    }


    public Result uploadWork(VoiceWorkVo vo) {

        if (vo == null) {
            return Result.fail("数据不能为空");
        }
        File file = ossService.getObjectFile(null, vo.getFileUrl());
        ossService.deleteObject(null, vo.getFileUrl());
        String url = ossService.putFileToName(null, file, "voice", vo.getTitle());

        //生成语音包
        VoicePacket voicePacket = new VoicePacket();
        voicePacket.setShopId(vo.getShopId());
        voicePacket.setName(vo.getTitle());
        voicePacket.setVoiceTime(vo.getVoiceTime());
        voicePacket.setFileUrl(url);
        VoicePacket saveVoicePacket = voicePacketRepository.save(voicePacket);


        //生成作品
        VoiceWork voiceWork = new VoiceWork();
        voiceWork.setTitle(vo.getTitle());
        voiceWork.setContent(vo.getContent());

        voiceWork.setShopId(vo.getShopId());
        voiceWork.setVoiceId(saveVoicePacket.getId());
        voiceWork.setAnchorId(vo.getAnchorId());
        voiceWork.setVoiceTime(vo.getVoiceTime());
        voiceWork.setSpeed(vo.getSpeed());
        voiceWork.setVolume(vo.getVolume());
        voiceWork.setPitch(vo.getPitch());
        voiceWork.setBackgroundMusicVolume(vo.getBackgroundMusicVolume());
        voiceWork.setBackgroundMusicId(vo.getBackgroundMusicId());
        voiceWork.setUserId(vo.getUserId());
        voiceWork.setDelStatus(0);
        voiceWork.setCreateTime(new Date());
        String sampleRate = redisService.getValue(Constant.SAMPLE_RATE);
        if (sampleRate != null) {
            if (sampleRate.equals("8000") || sampleRate.equals("16000") || sampleRate.equals("18000")) {
                voiceWork.setSampleRate(Integer.valueOf(sampleRate));
            }
        }
        VoiceWork saveVoiceWork = voiceWorkRepository.save(voiceWork);

        saveVoicePacket.setVoiceWorkId(saveVoiceWork.getId());
        voicePacketRepository.save(saveVoicePacket);

        return Result.success();
    }

    public Result update(VoiceWorkVo vo) {
        if (vo == null) {
            return Result.fail("数据不能为空");
        }
        if (vo.getId() == null) {
            return Result.fail("作品id不能为空");
        }
        if (StringUtils.isBlank(vo.getTitle())) {
            return Result.fail("作品标题不能为空");
        }
        if (StringUtils.isBlank(vo.getContent())) {
            return Result.fail("作品文字不能为空");
        }
        if (vo.getAnchorId() == null) {
            return Result.fail("主播id不能为空");
        }
        if (vo.getUserId() == null) {
            return Result.fail("所属用户id不能为空");
        }
        Optional<VoiceWork> byId = voiceWorkRepository.findById(vo.getId());
        if (!byId.isPresent()) {
            return Result.fail("作品不存在");
        }
        //修改作品
        VoiceWork voiceWork = byId.get();
        VoicePacket voicePacket = voicePacketRepository.findByVoiceWorkId(vo.getId());
        VoicePacket saveVoicePacket;
        File file = ossService.getObjectFile(null, vo.getFileUrl());
        ossService.deleteObject(null, vo.getFileUrl());
        String url = ossService.putFileNewName(null, file, "voice");
        if (voicePacket == null) {
            //没有则新增
            voicePacket = new VoicePacket();
            voicePacket.setName(vo.getTitle());
            voicePacket.setVoiceTime(vo.getVoiceTime());
            voicePacket.setFileUrl(url);
            saveVoicePacket = voicePacketRepository.save(voicePacket);
        } else {
            saveVoicePacket = voicePacket;
            voicePacket.setName(vo.getTitle());
            voicePacket.setVoiceTime(vo.getVoiceTime());
            voicePacket.setFileUrl(url);
            saveVoicePacket = voicePacketRepository.save(voicePacket);
        }

        voiceWork.setTitle(vo.getTitle());
        voiceWork.setContent(vo.getContent());
        voiceWork.setShopId(vo.getShopId());

        voiceWork.setVoiceId(saveVoicePacket.getId());
        voiceWork.setAnchorId(vo.getAnchorId());
        voiceWork.setVoiceTime(vo.getVoiceTime());
        voiceWork.setSpeed(vo.getSpeed());
        voiceWork.setVolume(vo.getVolume());
        voiceWork.setPitch(vo.getPitch());
        voiceWork.setBackgroundMusicVolume(vo.getBackgroundMusicVolume());
        voiceWork.setBackgroundMusicId(vo.getBackgroundMusicId());
        voiceWork.setSampleRate(vo.getSampleRate());
        voiceWork.setUserId(vo.getUserId());
        VoiceWork saveVoiceWork = voiceWorkRepository.save(voiceWork);

        saveVoicePacket.setVoiceWorkId(saveVoiceWork.getId());
        voicePacketRepository.save(saveVoicePacket);

        return Result.success();
    }

    /**
     * 批量删除，删除作品则要把oss上的文件语音包删除，还要删除语音包表
     *
     * @return
     */
    public Result delete(WorkDto dto) {
        if (dto == null) {
            return Result.fail("数据不能为空");
        }
        if (dto.getIds().isEmpty()) {
            return Result.fail("作品id不能为空");
        }
        for (Long id : dto.getIds()) {
            //查询出语音包路径
            VoicePacket packet = voicePacketRepository.findByVoiceWorkId(id);
            if (packet != null && ossService.doesObjectExist(packet.getFileUrl())) {
                ossService.deleteObject(null, packet.getFileUrl());
            }
            voiceWorkRepository.updateDelStatusById(id);//查询作品
            voicePacketRepository.deleteByVoiceWorkId(id);//删除语音包
        }
        return Result.success();
    }

    public void exportList(WorkDto dto, HttpServletResponse response) throws IOException {
        Result list = list(dto, 2);
        HashMap hashMap = JSONObject.parseObject(JSONObject.toJSONString(list.getData()), HashMap.class);
        Object result = hashMap.get("result");
        List<ExcelVoiceWork> excelVoiceWorkList = null;
        if (result != null) {
            List<VoiceWorkVo> voiceWorkVos = JSON.parseArray(JSON.toJSONString(result), VoiceWorkVo.class);
            excelVoiceWorkList = voiceWorkVos.stream().map(item -> {
                ExcelVoiceWork excelVoiceWork = new ExcelVoiceWork();
                excelVoiceWork.setTitle(item.getTitle());
                excelVoiceWork.setContent(item.getContent());
                excelVoiceWork.setFileUrl(StringUtils.isBlank(item.getFileUrl()) ? "" : item.getFileUrl());
                excelVoiceWork.setAnchorName(item.getAnchorName());
                excelVoiceWork.setVoiceTime(item.getVoiceTime() == null ? "" : item.getVoiceTime() + "s");
                excelVoiceWork.setSpeed(String.valueOf(item.getSpeed()));
                excelVoiceWork.setMusicName(item.getMusicName());
                excelVoiceWork.setNickname(StringUtils.isBlank(item.getNickname()) ? "" : item.getNickname());
                excelVoiceWork.setPhone(StringUtils.isBlank(item.getPhone()) ? "" : item.getPhone());
                excelVoiceWork.setCreateTime(item.getCreateTime() == null ? "" : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(item.getCreateTime()));
                return excelVoiceWork;
            }).collect(Collectors.toList());
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = "作品管理_" + System.currentTimeMillis();
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ExcelTypeEnum.XLSX.getValue());
        EasyExcel.write(response.getOutputStream(), ExcelVoiceWork.class).sheet("作品管理").doWrite(excelVoiceWorkList);
        //导出excel
        log.info("作品管理导出end");
    }

    public Result findTemplates() {
        String sqlType = "select id,name from dub_template_type where del_status != 1  ";
        List<TemplateTypeVo> templateTypeVos = jdbcTemplate.query(sqlType, new BeanPropertyRowMapper<>(TemplateTypeVo.class));

        List<Template> templates = templateRepository.findByDelStatus();
        Map<Long, List<Template>> listMap = templates.stream().collect(Collectors.groupingBy(Template::getTemplateTypeId));
        for (TemplateTypeVo templateTypeVo : templateTypeVos) {
            for (Long typeId : listMap.keySet()) {
                if (templateTypeVo.getId().equals(typeId)) {
                    templateTypeVo.setTemplates(listMap.get(typeId));
                }
            }
        }
        return Result.success(templateTypeVos);
    }

    public Result findAnchors(String searchCondition) {
        List<Object> args = new ArrayList<>();
        String sql = "select id,name,usage_scenario,voice_url,url from dub_anchor ";
        if (StringUtils.isNotBlank(searchCondition)) {
            sql += " where name like ?";
            args.add("%" + searchCondition + "%");
        }
        List<Anchor> anchors = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(Anchor.class), args);
        return Result.success(anchors);
    }

    public Result findLongAnchors(String searchCondition) {
        List<Object> args = new ArrayList<>();
        String sql = "select id,name,usage_scenario,voice_url,url from long_anchor ";
        if (StringUtils.isNotBlank(searchCondition)) {
            sql += " where name like ?";
            args.add("%" + searchCondition + "%");
        }
        List<LongAnchor> anchors = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(LongAnchor.class), args);
        return Result.success(anchors);
    }

    public Result findBackgroundMusic() {

        String sqlCate = "select * from dub_background_music_type";
        List<BackGroundMusicTypeVo> backGroundMusicTypeVos = jdbcTemplate.query(sqlCate, new BeanPropertyRowMapper<>(BackGroundMusicTypeVo.class));

        String sql = "select id,name,music_url,type_id,music_time from dub_background_music where del_status != 1 ";
        List<BackgroundMusic> backgroundMusics = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(BackgroundMusic.class));

        Map<Long, List<BackgroundMusic>> listMap = backgroundMusics.stream().collect(Collectors.groupingBy(BackgroundMusic::getTypeId));
        for (BackGroundMusicTypeVo backGroundMusicTypeVo : backGroundMusicTypeVos) {
            for (Long typeId : listMap.keySet()) {
                if (backGroundMusicTypeVo.getId().equals(typeId)) {
                    backGroundMusicTypeVo.setBackgroundMusics(listMap.get(typeId));
                }
            }
        }
        return Result.success(backGroundMusicTypeVos);
    }

    public Result findWechatUsers(String searchCondition) {
        List<Object> args = new ArrayList<>();
        String sql = "select id,nickname as nicknames,phone from dub_wechat_user ";
        if (StringUtils.isNotBlank(searchCondition)) {
            sql += " where nickname like ? or phone like ?";
            args.add("%" + searchCondition + "%");
            args.add("%" + searchCondition + "%");
        }

        List<WechatUser> wechatUsers = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(WechatUser.class), args);
        return Result.success(wechatUsers);
    }

    /**
     * 试听
     */
    public Result audition(SpeechSynthesizerDto dto) {
        Long userId = dto.getUserId();
        log.info("Anchor dto={}", JSON.toJSONString(dto));
        if (dto.getText().isEmpty() || dto.getText() == null) {
            return Result.fail("请填写需合成录音文本");
        }
        if (dto.getText().length() > 300) {
            return Result.fail("字数不能超过300字");
        }
        Anchor anchor = anchorRepository.findById(dto.getAnchorId()).get();
        if (anchor == null) {
            return Result.fail("请选择需合成录音主播");
        }
        String beforeDto = redisService.getvoiceRepeat(userId);
        Map<String, Object> map = new HashMap<>();
        dto.setVoice(anchor.getVoiceName());
        dto.setIsEmotion(anchor.getIsEmotion());
        dto.setEmotion(anchor.getEmotion());
        if (dto.getBgm() != null && !dto.getBgm().contains(ossUrl)) {
            dto.setBgm(ossUrl + dto.getBgm());
        }
        log.info("bgm={}", dto.getBgm());
        SpeechSynthesizerDto afterDto = new SpeechSynthesizerDto();
        BeanUtils.copyProperties(dto, afterDto);
        if (StringUtils.isNotBlank(beforeDto)) {
            log.info("beforeDto={},dto={}", beforeDto, dto);
            if (JSON.toJSONString(dto).equals(beforeDto)) {
                return Result.fail(20010, "重复合成");
            }
        }
        Integer number = redisService.getVoiceNumber(dto.getUserId());
        String text = dto.getText().replaceAll("\\s*", "");
        text = text.replace("\u00A0", "");
        String voiceName = FileRenameUtils.createVoiceName(text, number);
        redisService.putVoiceNumber(number, dto.getUserId());
        dto.setUrl(appUrl);
        dto.setType(anchor.getType());
        dto.setText(FileUtil.replacePauseTags(dto.getText()));
        log.info("转换后的text={}", dto.getText());
        String url = null;
        try {
            url = dubbingFactory.process(dto, redisService);
        } catch (Exception e) {
            log.error("合成错误{}", e);
            redisService.setvoiceRepeat(userId, "", 20000L);
            if (e instanceof RuntimeException) {
                return Result.fail(e.getMessage());
            }
            return Result.fail("合成失败, 请更换主播尝试");
        }
        File file = new File(url);
        Integer voiceTime = null;
        File targetFile = FileUtil.createFileIfNotExists(appUrl + "compound" + File.separator + file.getName());
        try {
            FfmpegUtil.voloumVipAudio(file, targetFile, 5);
            MP3File f = (MP3File) AudioFileIO.read(targetFile);
            MP3AudioHeader audioHeader = (MP3AudioHeader) f.getAudioHeader();
            voiceTime = audioHeader.getTrackLength();
            url = ossService.putFileOld(null, targetFile, "temp/" + userId);
            targetFile.delete();
        } catch (Exception e) {
            redisService.setvoiceRepeat(userId, "", 20000L);
            log.error(e.getMessage(), e);
            targetFile.delete();
            return Result.fail("合成失败, 请更换主播尝试");
        }
        map.put("url", url);
        map.put("time", voiceTime);
        map.put("voiceUrlName", voiceName);
        return Result.success(map);
    }

    public Result getWorkList() {
        Result result = Result.success();
        if (shopRefUtil.isNeedWxFilter()) {
            result.addData("total", 0);
            result.addData("result", new ArrayList<>());
            return result;
        }
        String sql = "select w.id,w.title name  " + "from dub_voice_work w " + "left join dub_shop s on s.id=w.shop_id " + " where w.del_status = 0 ";
        List<Long> shopRef = shopRefUtil.getShopRef();
        if (CollUtil.isNotEmpty(shopRef)) {
            sql += " and  s.id in " + SqlUtils.foreach("(", ")", ",", shopRef);
        }
        List<ShopSelectListVo> list = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(ShopSelectListVo.class));
        return Result.success(list);
    }

    public Result sendWorkMusic(SendWorkMusicParams vo) {
        if (CollUtil.isEmpty(vo.getDeviceIds())) {
            return Result.fail("请选择设备");
        }
        if (vo.getId() == null) {
            return Result.fail("请选择作品");
        }
        if (StringUtils.isBlank(vo.getFileUrl())) {
            return Result.fail("请上传作品");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();

        String timeStamp = idGeneratorService.getTimestamp();

        List<Device> devs = deviceRepository.findAllById(vo.getDeviceIds());
        for (Device dev : devs) {
            if (StringUtils.isNotEmpty(dev.getSn())) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("cmd", "url_cmd");
                jsonObject.put("parm", ossUrl + vo.getFileUrl());
                jsonObject.put("timeStamp", timeStamp);

                log.info("发送MQTT消息: {}", JSONObject.toJSONString(jsonObject));
                mqttClientConfig.publish(dev.getSn(), jsonObject.toJSONString(), user.getId(), timeStamp, 15);
            }
        }
        return Result.success();
    }

    public Result longCompound(SpeechSynthesizerDto dto) {
        log.info("Anchor dto={}", JSON.toJSONString(dto));
        Map<String, Object> map = new HashMap<>();
        Long userId = dto.getUserId();
        String beforeDto = redisService.getvoiceRepeat(userId);
        LongAnchor anchor = longAnchorRepository.findById(dto.getAnchorId()).get();
        dto.setVoice(anchor.getVoiceName());
        if (dto.getText().length() > 2000) {
            return Result.fail("字数不能超过2000字");
        }
        SpeechSynthesizerDto afterDto = new SpeechSynthesizerDto();
        BeanUtils.copyProperties(dto, afterDto);
        if (StringUtils.isNotBlank(beforeDto)) {
            if (JSON.toJSONString(dto).equals(beforeDto)) {
                return Result.fail(20010, "重复合成");
            }
        }
        if (dto.getText().equals("") || dto.getText() == null) {
            return Result.fail("请填写需合成录音文本");
        }
        if (dto.getVoice().equals("") || dto.getVoice() == null) {
            return Result.fail("请选择需合成录音主播");
        }
        if (dto.getBgm() != null && !dto.getBgm().contains(ossUrl)) {
            dto.setBgm(ossUrl + dto.getBgm());
        }
        log.info("bgm={}", dto.getBgm());
        Integer number = redisService.getVoiceNumber(dto.getUserId());
        String text = dto.getText().replaceAll("\\s*", "");
        text = text.replace("\u00A0", "");
        String voiceName = FileRenameUtils.createVoiceName(text, number);
        dto.setUrl(appUrl);
        dto.setType(anchor.getType());
        String url = null;
        try {
            url = dubbingFactory.process(dto, redisService);
        } catch (Exception e) {
            redisService.setvoiceRepeat(userId, "", 20000L);
            return Result.fail("合成失败, 请更换主播尝试");
        }
        File file = new File(url);
        Integer voiceTime = null;
        try {
            MP3File f = (MP3File) AudioFileIO.read(file);
            MP3AudioHeader audioHeader = (MP3AudioHeader) f.getAudioHeader();
            voiceTime = audioHeader.getTrackLength();
            url = ossService.putFileOld(null, file, "temp/" + userId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e.getMessage().contains("No audio header found")) {
                file.delete();
                return Result.fail("合成失败, 请更换主播尝试");
            }
        }
        map.put("url", url);
        map.put("time", voiceTime);
        map.put("voiceUrlName", voiceName);
        log.info("合成语音的文件删除状态{}", file.delete());
        redisService.setvoiceRepeat(userId, JSON.toJSONString(afterDto), 20000L);
        return Result.success(map);
    }

    public Result dialog(SpeechSynthesizerDto dto) {
        log.info("Anchor dto={}", JSON.toJSONString(dto));
        Long userId = SecurityUtils.getLoginUser().getUser().getSiteId();
        Anchor anchor = anchorRepository.findById(dto.getAnchorId()).get();
        dto.setVoice(anchor.getVoiceName());
        if (anchor.getType().equalsIgnoreCase(DubbingTypeEnum.DY_DUBBING.name())) {
            dto.setVolume(dto.getVolume() - 70);
        } else if (anchor.getType().equalsIgnoreCase(DubbingTypeEnum.TX_SHOT_DUBBING.name())) {
            dto.setVolume(dto.getVolume() - 30);
        }
        if (dto.getText().length() > 300) {
            return Result.fail("字数不能超过300字");
        }
        Map<String, Object> map = new HashMap<>();
        if (dto.getText().equals("") || dto.getText() == null) {
            return Result.fail("请填写需合成录音文本");
        }
        if (dto.getVoice().equals("") || dto.getVoice() == null) {
            return Result.fail("请选择需合成录音主播");
        }
        if (dto.getBgm() != null && !dto.getBgm().contains(ossUrl)) {
            dto.setBgm(ossUrl + dto.getBgm());
        }
        Integer number = redisService.getVoiceNumber(dto.getUserId());
        String text = dto.getText().replaceAll("\\s*", "");
        text = text.replace("\u00A0", "");
        String voiceName = FileRenameUtils.createVoiceName(text, number);
        redisService.putVoiceNumber(number, dto.getUserId());
        log.info("bgm={}", dto.getBgm());
        dto.setUrl(appUrl);
        dto.setType(anchor.getType());
        dto.setText(FileUtil.replacePauseTags(dto.getText()));
        log.info("转换后的text={}", dto.getText());
        String url = null;
        try {
            url = dubbingFactory.process(dto, redisService);
        } catch (Exception e) {
            redisService.setvoiceRepeat(userId, "", 20000L);
            return Result.fail("合成失败, 请更换主播尝试");
        }
        File file = new File(url);
        Integer voiceTime = null;
        try {
            MP3File f = (MP3File) AudioFileIO.read(file);
            MP3AudioHeader audioHeader = (MP3AudioHeader) f.getAudioHeader();
            voiceTime = audioHeader.getTrackLength();
            url = ossService.putFileOld(null, file, "temp/" + userId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e.getMessage().contains("No audio header found")) {
                file.delete();
                return Result.fail("合成失败, 请更换主播尝试");
            }
        }
        map.put("url", url);
        map.put("time", voiceTime);
        map.put("voiceUrlName", voiceName);
        file.delete();
        return Result.success(map);
    }

    public Result mergeAudio(MergeVo mergeVo) {
        if (mergeVo.getBgm() != null && !mergeVo.getBgm().contains(ossUrl)) {
            mergeVo.setBgm(ossUrl + mergeVo.getBgm());
        }
        Long userId = SecurityUtils.getLoginUser().getUser().getSiteId();
        Map<String, Object> map = new HashMap<>();
        Integer number = redisService.getVoiceNumber(userId);
        String text = mergeVo.getText();
        List<String> audioList = mergeVo.getAudio();
        String voiceName = FileRenameUtils.createVoiceName(text, number);
        redisService.putVoiceNumber(number, userId);
        String tempFileName = appUrl + FileUtil.getFileNewName(".mp3");
        String outFileName = appUrl + FileUtil.getFileNewName(".mp3");
        String fileName = appUrl + FileUtil.getFileNewName(".mp3");
        File file = new File(fileName);
        try {
            if (!StringUtils.isEmpty(mergeVo.getBgm())) {
                String wavFileName = appUrl + FileUtil.getFileNewName(".wav");
                File outFile = combine(outFileName, audioList, tempFileName);
                File wavFile = new File(wavFileName);
                FileUtil.coverToWav(outFile, wavFile);
                String bgmFileName = appUrl + FileUtil.getFileNewName(".wav");
                File bgmFile = FileUtil.taiseng(mergeVo.getBgm(), bgmFileName, mergeVo.getBugRate());
                File resFile = new File(appUrl + FileUtil.getFileNewName(".wav"));
                FfmpegUtil.mixBgm(wavFile, bgmFile, resFile, mergeVo.getBeforeDelay(), mergeVo.getAfterDelay(), mergeVo.getBgmCenterVolum());
                FileUtil.coverToMp3(resFile, file);
            } else {
                combine(fileName, audioList, tempFileName);
            }
            String url = ossService.putFileOld(null, file, "temp/" + userId);
            file.delete();
            map.put("time", 0);
            map.put("url", url);
            map.put("voiceUrlName", voiceName);
            return Result.success(map);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public File combine(String outFile, List<String> files, String tempFile) throws Exception {
        Path tempFilePath = Paths.get(outFile);
        FileOutputStream fos = new FileOutputStream(tempFilePath.toFile(), true);
        for (int i = 0; i < files.size(); i++) {
            File file = downloadFile(files.get(i), tempFile);
            FileInputStream fis = new FileInputStream(file);
            int len = 0;
            for (byte[] buf = new byte[1024 * 1024]; (len = fis.read(buf)) != -1; ) {
                fos.write(buf, 0, len);
            }
            fis.close();
            boolean delete = file.delete();
            log.info("对话配音删除合成的临时文件：{}", delete);
        }
        fos.close();
        return tempFilePath.toFile();
    }

    private File downloadFile(String fileURL, String voiceName) throws Exception {
        URL url = new URL(ossUrl + fileURL);
        HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
        int responseCode = httpConn.getResponseCode();

        // 检查HTTP响应代码
        if (responseCode == HttpURLConnection.HTTP_OK) {
            String tempFileName = voiceName; // 临时文件名
            Path tempFilePath = Paths.get(tempFileName);
            try (InputStream inputStream = new BufferedInputStream(httpConn.getInputStream()); FileOutputStream outputStream = new FileOutputStream(tempFilePath.toFile())) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            } finally {
                httpConn.disconnect();
            }

            return tempFilePath.toFile();
        } else {
            throw new Exception("GET request not worked: " + responseCode);
        }
    }

    public Result aiClassList() {
        List<AiClass> aiClassList = aiClassRepository.findByDelStatus(0);
        return Result.success(aiClassList);
    }

    public Result aiStyleList() {
        List<AiStyle> aiStyleList = aiStyleRepository.findByDelStatus(0);
        return Result.success(aiStyleList);
    }

    public Result aiLanguageList() {
        List<AiLanguage> aiLanguageList = aiLanguageRepository.findByDelStatus(0);
        return Result.success(aiLanguageList);
    }

    public Result uploadVideo(MultipartFile file, String name) {
        if (file.getSize() > 1024 * 1024 * 500) {
            return Result.fail("文件最大上传500M");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        Long userId = user.getSiteId();
        try {
            int index = name.lastIndexOf(".");
            String fileName = name.substring(0, index);
            fileName = fileName.replaceAll("\\s*", "");
            fileName = FileRenameUtils.cleanText(fileName);
            if (fileName.length() > 4) {
                fileName = fileName.substring(0, 4);
            }
            File processFile = new File(appUrl + userId + "&" + name);
            File sourceFile = new File(appUrl + userId + "&" + fileName + ".wav");
            FileUtil.tempCoverToWav(file, sourceFile, processFile);
            File targetFile = new File(appUrl + userId + fileName + ".wav");
            FileUtil.trunFile(sourceFile, targetFile);
            String url = ossService.dijia(null, targetFile, "bgm", userId + "/" + fileName);
            sourceFile.delete();
            targetFile.delete();
            UserBackgroundMusic userBackgroundMusic = new UserBackgroundMusic();
            userBackgroundMusic.setName(fileName);
            userBackgroundMusic.setUserId(userId);
            userBackgroundMusic.setDelStatus(0);
            userBackgroundMusic.setCreateTime(new Date());
            userBackgroundMusic.setMusicUrl(url);
            userBackgroundMusicRepository.save(userBackgroundMusic);
            return Result.success(null);
        } catch (Exception e) {
            return Result.fail("上传失败");
        }
    }

    public Result myMusic() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        Long userId = user.getSiteId();
        List<UserBackgroundMusic> userBackgroundMusicList = userBackgroundMusicRepository.findByUserId(userId);
        List<UserBackgroundMusicVo> userBackgroundMusicVos = userBackgroundMusicList.stream().map(obj -> {
            UserBackgroundMusicVo userBackgroundMusicVo = new UserBackgroundMusicVo();
            BeanUtils.copyProperties(obj, userBackgroundMusicVo);
            userBackgroundMusicVo.setUrl(obj.getMusicUrl());
            return userBackgroundMusicVo;
        }).collect(Collectors.toList());
        Map<String, Object> map = new HashMap<>(1);
        map.put("result", userBackgroundMusicVos);
        return Result.success(map);
    }

    public Result delMyMusic(Long musicId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUserVo user = loginUser.getUser();
        Long userId = user.getSiteId();
        userBackgroundMusicRepository.deleteByUserIdAndId(userId, musicId);
        return Result.success(true);
    }
}
