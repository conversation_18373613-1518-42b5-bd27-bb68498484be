package com.gl.service.template.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.web.response.Result;
import com.gl.service.template.service.TemplateService;
import com.gl.service.template.vo.TemplateVo;
import com.gl.service.template.vo.dto.TemplateDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * TemplateController简单单元测试类
 * 不依赖Spring上下文，直接测试控制器逻辑
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("模板控制器简单单元测试")
class TemplateControllerSimpleTest {

    @Mock
    private TemplateService templateService;

    @InjectMocks
    private TemplateController templateController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;
    private TemplateDto mockTemplateDto;
    private TemplateVo mockTemplateVo;
    private Result mockSuccessResult;
    private Result mockFailResult;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(templateController).build();
        objectMapper = new ObjectMapper();

        // 初始化模拟DTO数据
        mockTemplateDto = new TemplateDto();
        mockTemplateDto.setShopId(100L);
        mockTemplateDto.setTemplateTypeId(1L);
        mockTemplateDto.setSearchCondition("测试");
        mockTemplateDto.setIds(Arrays.asList(1L, 2L, 3L));

        // 初始化模拟VO数据
        mockTemplateVo = new TemplateVo();
        mockTemplateVo.setId(1L);
        mockTemplateVo.setTemplateTypeId(1L);
        mockTemplateVo.setTitle("测试模板");
        mockTemplateVo.setContent("测试内容");
        mockTemplateVo.setShopId(100L);
        mockTemplateVo.setCreateTime(new Date());

        // 初始化模拟结果数据
        mockSuccessResult = Result.success();
        mockSuccessResult.addData("total", 1);
        mockSuccessResult.addData("result", Arrays.asList(mockTemplateVo));

        mockFailResult = Result.fail("操作失败");
    }

    @Test
    @DisplayName("测试获取模板列表 - 成功获取")
    void testList_Success() throws Exception {
        // Given
        when(templateService.list(any(TemplateDto.class), eq(1))).thenReturn(mockSuccessResult);

        // When & Then
        mockMvc.perform(get("/template")
                .param("shopId", "100")
                .param("templateTypeId", "1")
                .param("searchCondition", "测试")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.total").value(1));

        verify(templateService, times(1)).list(any(TemplateDto.class), eq(1));
    }

    @Test
    @DisplayName("测试新增和修改模板 - 成功新增")
    void testAddAndUpdate_AddSuccess() throws Exception {
        // Given
        mockTemplateVo.setId(null); // 新增操作
        when(templateService.addAndUpdate(any(TemplateVo.class))).thenReturn(mockSuccessResult);

        // When & Then
        mockMvc.perform(post("/template")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(templateService, times(1)).addAndUpdate(any(TemplateVo.class));
    }

    @Test
    @DisplayName("测试新增和修改模板 - 成功修改")
    void testAddAndUpdate_UpdateSuccess() throws Exception {
        // Given
        when(templateService.addAndUpdate(any(TemplateVo.class))).thenReturn(mockSuccessResult);

        // When & Then
        mockMvc.perform(post("/template")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(templateService, times(1)).addAndUpdate(any(TemplateVo.class));
    }

    @Test
    @DisplayName("测试新增和修改模板 - 业务逻辑失败")
    void testAddAndUpdate_BusinessFail() throws Exception {
        // Given
        when(templateService.addAndUpdate(any(TemplateVo.class))).thenReturn(mockFailResult);

        // When & Then
        mockMvc.perform(post("/template")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10001))
                .andExpect(jsonPath("$.message").value("操作失败"));

        verify(templateService, times(1)).addAndUpdate(any(TemplateVo.class));
    }

    @Test
    @DisplayName("测试删除模板 - 成功删除")
    void testDelete_Success() throws Exception {
        // Given
        when(templateService.delete(any(TemplateDto.class))).thenReturn(mockSuccessResult);

        // When & Then
        mockMvc.perform(delete("/template")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(templateService, times(1)).delete(any(TemplateDto.class));
    }

    @Test
    @DisplayName("测试删除模板 - 业务逻辑失败")
    void testDelete_BusinessFail() throws Exception {
        // Given
        when(templateService.delete(any(TemplateDto.class))).thenReturn(mockFailResult);

        // When & Then
        mockMvc.perform(delete("/template")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10001))
                .andExpect(jsonPath("$.message").value("操作失败"));

        verify(templateService, times(1)).delete(any(TemplateDto.class));
    }

    @Test
    @DisplayName("测试导出模板列表 - 成功导出")
    void testExportList_Success() throws Exception {
        // Given
        doNothing().when(templateService).exportList(any(TemplateDto.class), any(HttpServletResponse.class));

        // When & Then
        mockMvc.perform(post("/template/export")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockTemplateDto)))
                .andDo(print())
                .andExpect(status().isOk());

        verify(templateService, times(1)).exportList(any(TemplateDto.class), any(HttpServletResponse.class));
    }

    // 注意：IOException异常测试被移除，因为控制器没有异常处理机制
    // 在实际应用中，应该在控制器中添加适当的异常处理

    @Test
    @DisplayName("测试获取模板类型下拉框列表 - 成功获取")
    void testFindTemplateType_Success() throws Exception {
        // Given
        when(templateService.findTemplateType()).thenReturn(mockSuccessResult);

        // When & Then
        mockMvc.perform(get("/template/type")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(templateService, times(1)).findTemplateType();
    }

    @Test
    @DisplayName("测试获取模板类型下拉框列表 - 业务逻辑失败")
    void testFindTemplateType_BusinessFail() throws Exception {
        // Given
        when(templateService.findTemplateType()).thenReturn(mockFailResult);

        // When & Then
        mockMvc.perform(get("/template/type")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10001))
                .andExpect(jsonPath("$.message").value("操作失败"));

        verify(templateService, times(1)).findTemplateType();
    }

    @Test
    @DisplayName("测试HTTP方法不支持")
    void testUnsupportedHttpMethod() throws Exception {
        // When & Then
        mockMvc.perform(put("/template")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isMethodNotAllowed());
    }

    @Test
    @DisplayName("测试内容类型不支持")
    void testUnsupportedMediaType() throws Exception {
        // When & Then
        mockMvc.perform(post("/template")
                .contentType(MediaType.TEXT_PLAIN)
                .content("plain text content"))
                .andDo(print())
                .andExpect(status().isUnsupportedMediaType());

        verify(templateService, never()).addAndUpdate(any(TemplateVo.class));
    }

    @Test
    @DisplayName("测试请求体格式错误")
    void testInvalidRequestBody() throws Exception {
        // When & Then
        mockMvc.perform(post("/template")
                .contentType(MediaType.APPLICATION_JSON)
                .content("invalid json"))
                .andDo(print())
                .andExpect(status().isBadRequest());

        verify(templateService, never()).addAndUpdate(any(TemplateVo.class));
    }
}
