09:08:13.627 [34mIN<PERSON><PERSON> [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
09:08:13.663 [34mINFO [0;39m [main] com.gl.ManagerApplication - Starting ManagerApplication using Java 1.8.0_382 on PC-20230417CHDY with PID 16416 (D:\yny\4g\admin\admin-manager\target\classes started by Administrator in D:\yny\4g\admin)
09:08:13.663 [34mINFO [0;39m [main] com.gl.ManagerApplication - The following profiles are active: dev
09:08:16.424 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
09:08:16.424 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
09:08:16.954 [34mINFO [0;39m [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 524 ms. Found 55 JPA repository interfaces.
09:08:17.287 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
09:08:17.297 [34mINFO [0;39m [main] o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
09:08:17.532 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@51cab489' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:08:17.541 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:08:17.552 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:08:17.562 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationChannelResolver' of type [org.springframework.integration.support.channel.BeanFactoryChannelResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:08:17.564 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:08:17.944 [34mINFO [0;39m [main] o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8770 (http)
09:08:17.953 [34mINFO [0;39m [main] o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8770"]
09:08:17.954 [34mINFO [0;39m [main] o.a.catalina.core.StandardService - Starting service [Tomcat]
09:08:17.954 [34mINFO [0;39m [main] o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.48]
09:08:18.115 [34mINFO [0;39m [main] o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
09:08:18.115 [34mINFO [0;39m [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4385 ms
09:08:18.550 [34mINFO [0;39m [main] o.s.b.web.servlet.RegistrationBean - Filter repeatableFilter was not registered (disabled)
09:08:18.600 [34mINFO [0;39m [main] c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
